# Build Rot Setup Guide

## Quick Setup

1. **Run the setup script:**
   ```bash
   ./setup.sh
   ```

2. **Set up Supabase:**
   - Create a new project at [supabase.com](https://supabase.com)
   - Go to Settings > API and copy your URL and anon key
   - Run the SQL from `database-schema.sql` in your Supabase SQL editor

3. **Configure environment:**
   ```bash
   cd backend
   # Edit .env file with your Supabase credentials
   nano .env
   ```

4. **Start the servers:**
   ```bash
   # Terminal 1: Backend
   cd backend && npm run dev

   # Terminal 2: Frontend  
   cd build-rot-frontend && npm run dev
   ```

5. **Visit http://localhost:3000**

## Visual Builder Features

### Card Types

**Events (Starting Points):**
- **Message Received** - Triggers when a Discord message is received
- **Member Joined** - Triggers when someone joins the server

**HTTP Requests:**
- **HTTP GET** - Make GET requests with variable support
- **HTTP POST** - Make POST requests with JSON body
- **HTTP PUT** - Make PUT requests for updates
- **HTTP DELETE** - Make DELETE requests

**Discord Actions:**
- **Send Message** - Send a message to a channel
- **Reply to Message** - Reply to the original message

**Logic & Control:**
- **Condition** - Branch execution based on conditions
- **Filter** - Filter data based on criteria
- **Delay** - Add delays between actions

**Utilities:**
- **Set Variable** - Store values in variables

### Variable System

Use variables in your cards with `{{VARIABLE_NAME}}` syntax:

**Built-in Variables:**
- `{{BOT_ID}}` - Your bot's ID
- `{{BOT_NAME}}` - Your bot's name
- `{{SERVER_URL}}` - Backend server URL

**Discord Variables:**
- `{{author}}` - Message author username
- `{{author.id}}` - Message author ID
- `{{content}}` - Message content
- `{{channel}}` - Channel name
- `{{guild}}` - Server name

**Custom Variables:**
Add your own variables in the Variables panel.

### Example Flows

**1. Simple Auto-Reply Bot:**
```
Message Received → Reply to Message
```

**2. Welcome Bot with API Call:**
```
Member Joined → HTTP POST (to webhook) → Send Message
```

**3. Conditional Response:**
```
Message Received → Condition → Send Message (if true)
                            → Reply Message (if false)
```

**4. Bot Management via HTTP:**
```
Message Received → HTTP GET (check bot status) → Condition → HTTP POST (start/stop bot)
```

### HTTP Examples

**Get Bot Status:**
- URL: `https://localhost:5000/api/bots/{{BOT_ID}}`
- Headers: `{"Authorization": "Bearer {{TOKEN}}"}`

**Start Bot:**
- URL: `https://localhost:5000/api/bots/{{BOT_ID}}/start`
- Method: POST
- Headers: `{"Authorization": "Bearer {{TOKEN}}"}`

**Send Webhook:**
- URL: `https://discord.com/api/webhooks/{{WEBHOOK_ID}}/{{WEBHOOK_TOKEN}}`
- Method: POST
- Body: `{"content": "New member: {{author}}"}`

## Tips

1. **Connect Matching Types:** Only connect outputs to inputs of the same type (text→text, object→object, etc.)

2. **Use Variables:** Leverage the variable system for dynamic content and API endpoints

3. **Test Incrementally:** Build simple flows first, then add complexity

4. **Export Code:** Use the "Export Code" button to see the generated JavaScript

5. **Save Often:** Use the Save button to persist your visual flows

## Troubleshooting

**Cards won't connect:**
- Check that output and input types match
- Ensure you're dragging from output (right side) to input (left side)

**Variables not working:**
- Make sure variable names are exact (case-sensitive)
- Use double curly braces: `{{VARIABLE_NAME}}`

**HTTP requests failing:**
- Check URL format and variables
- Verify headers are valid JSON
- Ensure the target API is accessible

**Generated code issues:**
- Review the exported code for syntax errors
- Check that all required variables are defined
- Validate JSON in HTTP request bodies
