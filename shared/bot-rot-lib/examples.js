const { createBot } = require('./index.js');

const bot = createBot('YOUR_BOT_TOKEN');

bot.onReady(() => {
    console.log('My bot is online!');
});

bot.onMessage((message) => {
    if (message.content === '!hello') {
        message.reply('Hello there!');
    }
});

bot.command('ping', 'Check bot latency')
    .run(async (interaction) => {
        await bot.reply(interaction, `Pong! Latency: ${bot.client.ws.ping}ms`);
    });

bot.command('userinfo', 'Get information about a user', [
    { name: 'user', description: 'The user to get info about', type: 'user', required: false }
])
    .run(async (interaction) => {
        const user = interaction.options.getUser('user') || interaction.user;
        const embed = bot.embed(
            `User Info: ${user.username}`,
            `**ID:** ${user.id}\n**Created:** ${user.createdAt.toDateString()}`,
            '#00ff00'
        );
        await bot.reply(interaction, '', { embed });
    });

bot.command('welcome', 'Send a welcome message with buttons')
    .run(async (interaction) => {
        const embed = bot.embed(
            'Welcome!',
            'Click a button below to get started',
            '#ff9900'
        );
        
        const button1 = bot.button('Get Help', 'help_button', 'Primary');
        const button2 = bot.button('Join Community', 'community_button', 'Success');
        const row = bot.row(button1, button2);
        
        await bot.reply(interaction, '', { embed, components: [row] });
    });

bot.onMemberJoin((member) => {
    const channel = member.guild.channels.cache.find(ch => ch.name === 'welcome');
    if (channel) {
        const embed = bot.embed(
            'New Member!',
            `Welcome ${member.user.username} to the server!`,
            '#00ff00'
        );
        bot.sendMessage(channel, '', { embed });
    }
});

bot.start();
