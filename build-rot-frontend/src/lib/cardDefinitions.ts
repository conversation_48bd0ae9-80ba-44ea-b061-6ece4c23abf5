import {
  MessageSquare,
  User,
  Hash,
  Play,
  Globe,
  Send,
  Download,
  Upload,
  Database,
  Zap,
  Filter,
  GitBranch,
  Clock,
  Settings,
  X
} from 'lucide-react';

export interface CardInput {
  key: string;
  label: string;
  type: string;
  required?: boolean;
  editable?: boolean;
  inputType?: string;
  defaultValue?: string;
  placeholder?: string;
}

export interface CardOutput {
  key: string;
  label: string;
  type: string;
}

export interface CardConfig {
  key: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'number' | 'checkbox';
  inputType?: string;
  defaultValue?: string;
  placeholder?: string;
  options?: { label: string; value: string }[];
}

export interface CardDefinition {
  type: string;
  name: string;
  description: string;
  category: string;
  icon: any;
  color: string;
  inputs: CardInput[];
  outputs: CardOutput[];
  configurable: boolean;
  config?: CardConfig[];
}

export const cardDefinitions: CardDefinition[] = [
  // Discord Events
  {
    type: 'message_received',
    name: 'Message Received',
    description: 'Triggers when a Discord message is received',
    category: 'Events',
    icon: MessageSquare,
    color: 'bg-blue-600',
    inputs: [],
    outputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' },
      { key: 'message', label: 'Message Object', type: 'message' },
      { key: 'content', label: 'Message Content', type: 'text' },
      { key: 'author', label: 'Author Object', type: 'user' },
      { key: 'author_name', label: 'Author Name', type: 'text' },
      { key: 'author_id', label: 'Author ID', type: 'text' },
      { key: 'channel', label: 'Channel Object', type: 'channel' },
      { key: 'channel_name', label: 'Channel Name', type: 'text' },
      { key: 'guild_name', label: 'Server Name', type: 'text' }
    ],
    configurable: true,
    config: [
      {
        key: 'trigger',
        label: 'Trigger Type',
        type: 'select',
        defaultValue: 'all',
        options: [
          { label: 'All Messages', value: 'all' },
          { label: 'Starts With', value: 'startsWith' },
          { label: 'Contains', value: 'contains' },
          { label: 'Exact Match', value: 'exact' }
        ]
      },
      {
        key: 'triggerValue',
        label: 'Trigger Value',
        type: 'text',
        placeholder: 'e.g., !hello'
      }
    ]
  },

  {
    type: 'member_join',
    name: 'Member Joined',
    description: 'Triggers when a member joins the Discord server',
    category: 'Events',
    icon: User,
    color: 'bg-green-600',
    inputs: [],
    outputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' },
      { key: 'member', label: 'Member Object', type: 'user' },
      { key: 'username', label: 'Username', type: 'text' },
      { key: 'user_id', label: 'User ID', type: 'text' },
      { key: 'display_name', label: 'Display Name', type: 'text' },
      { key: 'guild', label: 'Guild Object', type: 'object' },
      { key: 'guild_name', label: 'Server Name', type: 'text' },
      { key: 'member_count', label: 'Member Count', type: 'number' }
    ],
    configurable: false
  },

  // HTTP Requests
  {
    type: 'http_get',
    name: 'HTTP GET',
    description: 'Make a GET request to a URL with variable support',
    category: 'HTTP',
    icon: Download,
    color: 'bg-cyan-600',
    inputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' }
    ],
    outputs: [
      { key: 'response', label: 'Response', type: 'object' },
      { key: 'data', label: 'Data', type: 'any' },
      { key: 'status', label: 'Status', type: 'number' },
      { key: 'error', label: 'Error', type: 'text' }
    ],
    configurable: true,
    config: [
      {
        key: 'url',
        label: 'URL',
        type: 'text',
        placeholder: 'https://localhost:5001/api/bots/{{BOT_ID}}'
      },
      {
        key: 'headers',
        label: 'Headers (JSON)',
        type: 'textarea',
        placeholder: '{"Authorization": "Bearer {{TOKEN}}", "Content-Type": "application/json"}'
      }
    ]
  },

  {
    type: 'http_post',
    name: 'HTTP POST',
    description: 'Make a POST request to a URL with variable support',
    category: 'HTTP',
    icon: Upload,
    color: 'bg-indigo-600',
    inputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' },
      { key: 'data', label: 'Data', type: 'object' }
    ],
    outputs: [
      { key: 'response', label: 'Response', type: 'object' },
      { key: 'data', label: 'Data', type: 'any' },
      { key: 'status', label: 'Status', type: 'number' },
      { key: 'error', label: 'Error', type: 'text' }
    ],
    configurable: true,
    config: [
      {
        key: 'url',
        label: 'URL',
        type: 'text',
        placeholder: 'https://localhost:5001/api/bots/{{BOT_ID}}/start'
      },
      {
        key: 'body',
        label: 'Request Body (JSON)',
        type: 'textarea',
        placeholder: '{"message": "{{content}}", "userId": "{{author.id}}"}'
      },
      {
        key: 'headers',
        label: 'Headers (JSON)',
        type: 'textarea',
        placeholder: '{"Content-Type": "application/json", "Authorization": "Bearer {{TOKEN}}"}'
      }
    ]
  },

  {
    type: 'http_put',
    name: 'HTTP PUT',
    description: 'Make a PUT request to a URL with variable support',
    category: 'HTTP',
    icon: Settings,
    color: 'bg-violet-600',
    inputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' },
      { key: 'data', label: 'Data', type: 'object' }
    ],
    outputs: [
      { key: 'response', label: 'Response', type: 'object' },
      { key: 'data', label: 'Data', type: 'any' },
      { key: 'status', label: 'Status', type: 'number' },
      { key: 'error', label: 'Error', type: 'text' }
    ],
    configurable: true,
    config: [
      {
        key: 'url',
        label: 'URL',
        type: 'text',
        placeholder: 'https://localhost:5001/api/bots/{{BOT_ID}}'
      },
      {
        key: 'body',
        label: 'Request Body (JSON)',
        type: 'textarea',
        placeholder: '{"name": "{{BOT_NAME}}", "description": "Updated bot"}'
      },
      {
        key: 'headers',
        label: 'Headers (JSON)',
        type: 'textarea',
        placeholder: '{"Content-Type": "application/json", "Authorization": "Bearer {{TOKEN}}"}'
      }
    ]
  },

  {
    type: 'http_delete',
    name: 'HTTP DELETE',
    description: 'Make a DELETE request to a URL with variable support',
    category: 'HTTP',
    icon: X,
    color: 'bg-red-600',
    inputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' }
    ],
    outputs: [
      { key: 'response', label: 'Response', type: 'object' },
      { key: 'status', label: 'Status', type: 'number' },
      { key: 'success', label: 'Success', type: 'boolean' },
      { key: 'error', label: 'Error', type: 'text' }
    ],
    configurable: true,
    config: [
      {
        key: 'url',
        label: 'URL',
        type: 'text',
        placeholder: 'https://localhost:5001/api/bots/{{BOT_ID}}'
      },
      {
        key: 'headers',
        label: 'Headers (JSON)',
        type: 'textarea',
        placeholder: '{"Authorization": "Bearer {{TOKEN}}"}'
      }
    ]
  },

  // Discord Actions
  {
    type: 'send_message',
    name: 'Send Message',
    description: 'Send a message to a Discord channel',
    category: 'Actions',
    icon: Send,
    color: 'bg-purple-600',
    inputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' },
      { key: 'channel', label: 'Channel', type: 'channel' },
      { key: 'content', label: 'Message Text', type: 'text' },
      { key: 'username', label: 'Username (optional)', type: 'text' },
      { key: 'user_id', label: 'User ID (optional)', type: 'text' }
    ],
    outputs: [
      { key: 'message', label: 'Sent Message', type: 'message' },
      { key: 'success', label: 'Success', type: 'boolean' },
      { key: 'continue', label: 'Continue', type: 'any' }
    ],
    configurable: true,
    config: [
      {
        key: 'content',
        label: 'Message Content',
        type: 'textarea',
        placeholder: 'Welcome {{username}} to {{guild_name}}! You are member #{{member_count}}.'
      },
      {
        key: 'channelName',
        label: 'Channel Name (if no channel input)',
        type: 'text',
        placeholder: 'general'
      }
    ]
  },

  {
    type: 'reply_message',
    name: 'Reply to Message',
    description: 'Reply to the original Discord message',
    category: 'Actions',
    icon: MessageSquare,
    color: 'bg-pink-600',
    inputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' },
      { key: 'message', label: 'Original Message', type: 'message' },
      { key: 'content', label: 'Reply Text', type: 'text' },
      { key: 'author_name', label: 'Author Name (optional)', type: 'text' }
    ],
    outputs: [
      { key: 'reply', label: 'Reply Message', type: 'message' },
      { key: 'success', label: 'Success', type: 'boolean' },
      { key: 'continue', label: 'Continue', type: 'any' }
    ],
    configurable: true,
    config: [
      {
        key: 'content',
        label: 'Reply Content',
        type: 'textarea',
        placeholder: 'Thanks for your message, {{author_name}}!'
      }
    ]
  },

  {
    type: 'get_channel',
    name: 'Get Channel',
    description: 'Get a channel by name or ID',
    category: 'Actions',
    icon: Hash,
    color: 'bg-teal-600',
    inputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' },
      { key: 'channel_name', label: 'Channel Name', type: 'text' }
    ],
    outputs: [
      { key: 'channel', label: 'Channel Object', type: 'channel' },
      { key: 'channel_name', label: 'Channel Name', type: 'text' },
      { key: 'channel_id', label: 'Channel ID', type: 'text' },
      { key: 'found', label: 'Found', type: 'boolean' }
    ],
    configurable: true,
    config: [
      {
        key: 'channelName',
        label: 'Channel Name',
        type: 'text',
        placeholder: 'general'
      }
    ]
  },

  {
    type: 'text_format',
    name: 'Format Text',
    description: 'Format text with variables and templates',
    category: 'Actions',
    icon: Settings,
    color: 'bg-amber-600',
    inputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' },
      { key: 'username', label: 'Username', type: 'text' },
      { key: 'user_id', label: 'User ID', type: 'text' },
      { key: 'guild_name', label: 'Server Name', type: 'text' },
      { key: 'member_count', label: 'Member Count', type: 'number' },
      { key: 'custom_value', label: 'Custom Value', type: 'text' }
    ],
    outputs: [
      { key: 'formatted_text', label: 'Formatted Text', type: 'text' },
      { key: 'continue', label: 'Continue', type: 'any' }
    ],
    configurable: true,
    config: [
      {
        key: 'template',
        label: 'Text Template',
        type: 'textarea',
        placeholder: 'Welcome {{username}} to {{guild_name}}! You are member #{{member_count}}. {{custom_value}}'
      }
    ]
  },

  // Logic & Control
  {
    type: 'condition',
    name: 'Condition',
    description: 'Check a condition and branch execution',
    category: 'Logic',
    icon: GitBranch,
    color: 'bg-orange-600',
    inputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' },
      { key: 'value1', label: 'Value 1', type: 'any', editable: true },
      { key: 'value2', label: 'Value 2', type: 'any', editable: true }
    ],
    outputs: [
      { key: 'true', label: 'True', type: 'any' },
      { key: 'false', label: 'False', type: 'any' }
    ],
    configurable: true,
    config: [
      {
        key: 'operator',
        label: 'Operator',
        type: 'select',
        defaultValue: 'equals',
        options: [
          { label: 'Equals', value: 'equals' },
          { label: 'Not Equals', value: 'notEquals' },
          { label: 'Contains', value: 'contains' },
          { label: 'Starts With', value: 'startsWith' },
          { label: 'Greater Than', value: 'greaterThan' },
          { label: 'Less Than', value: 'lessThan' }
        ]
      }
    ]
  },

  {
    type: 'filter',
    name: 'Filter',
    description: 'Filter data based on conditions',
    category: 'Logic',
    icon: Filter,
    color: 'bg-yellow-600',
    inputs: [
      { key: 'data', label: 'Data', type: 'any' },
      { key: 'condition', label: 'Condition', type: 'text', editable: true }
    ],
    outputs: [
      { key: 'filtered', label: 'Filtered Data', type: 'any' },
      { key: 'count', label: 'Count', type: 'number' }
    ],
    configurable: true,
    config: [
      {
        key: 'property',
        label: 'Property to Filter',
        type: 'text',
        placeholder: 'username'
      },
      {
        key: 'value',
        label: 'Filter Value',
        type: 'text',
        placeholder: 'admin'
      }
    ]
  },

  // Utilities
  {
    type: 'delay',
    name: 'Delay',
    description: 'Add a delay before continuing',
    category: 'Utilities',
    icon: Clock,
    color: 'bg-gray-600',
    inputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' }
    ],
    outputs: [
      { key: 'continue', label: 'Continue', type: 'any' }
    ],
    configurable: true,
    config: [
      {
        key: 'duration',
        label: 'Duration (seconds)',
        type: 'number',
        defaultValue: '1'
      }
    ]
  },

  {
    type: 'variable_set',
    name: 'Set Variable',
    description: 'Set a variable value',
    category: 'Utilities',
    icon: Database,
    color: 'bg-teal-600',
    inputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' },
      { key: 'value', label: 'Value', type: 'any', editable: true }
    ],
    outputs: [
      { key: 'variable', label: 'Variable', type: 'any' },
      { key: 'continue', label: 'Continue', type: 'any' }
    ],
    configurable: true,
    config: [
      {
        key: 'variableName',
        label: 'Variable Name',
        type: 'text',
        placeholder: 'myVariable'
      },
      {
        key: 'value',
        label: 'Value',
        type: 'text',
        placeholder: 'Hello World'
      }
    ]
  }
];

export function getCardDefinition(type: string): CardDefinition {
  return cardDefinitions.find(def => def.type === type) || cardDefinitions[0];
}

export function getCardsByCategory(): Record<string, CardDefinition[]> {
  return cardDefinitions.reduce((acc, card) => {
    if (!acc[card.category]) {
      acc[card.category] = [];
    }
    acc[card.category].push(card);
    return acc;
  }, {} as Record<string, CardDefinition[]>);
}
