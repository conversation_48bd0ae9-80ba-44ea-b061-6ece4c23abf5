import {
  MessageSquare,
  User,
  Hash,
  Play,
  Globe,
  Send,
  Download,
  Upload,
  Database,
  Zap,
  Filter,
  GitBranch,
  Clock,
  Settings,
  X
} from 'lucide-react';

export interface CardInput {
  key: string;
  label: string;
  type: string;
  required?: boolean;
  editable?: boolean;
  inputType?: string;
  defaultValue?: string;
  placeholder?: string;
}

export interface CardOutput {
  key: string;
  label: string;
  type: string;
}

export interface CardConfig {
  key: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'number' | 'checkbox';
  inputType?: string;
  defaultValue?: string;
  placeholder?: string;
  options?: { label: string; value: string }[];
}

export interface CardDefinition {
  type: string;
  name: string;
  description: string;
  category: string;
  icon: any;
  color: string;
  inputs: CardInput[];
  outputs: CardOutput[];
  configurable: boolean;
  config?: CardConfig[];
}

export const cardDefinitions: CardDefinition[] = [
  // Discord Events
  {
    type: 'message_received',
    name: 'Message Received',
    description: 'Triggers when a message is received',
    category: 'Events',
    icon: MessageSquare,
    color: 'bg-blue-600',
    inputs: [],
    outputs: [
      { key: 'message', label: 'Message', type: 'message' },
      { key: 'content', label: 'Content', type: 'text' },
      { key: 'author', label: 'Author', type: 'user' },
      { key: 'channel', label: 'Channel', type: 'channel' }
    ],
    configurable: true,
    config: [
      {
        key: 'trigger',
        label: 'Trigger Type',
        type: 'select',
        defaultValue: 'all',
        options: [
          { label: 'All Messages', value: 'all' },
          { label: 'Starts With', value: 'startsWith' },
          { label: 'Contains', value: 'contains' },
          { label: 'Exact Match', value: 'exact' }
        ]
      },
      {
        key: 'triggerValue',
        label: 'Trigger Value',
        type: 'text',
        placeholder: 'e.g., !hello'
      }
    ]
  },

  {
    type: 'member_join',
    name: 'Member Joined',
    description: 'Triggers when a member joins the server',
    category: 'Events',
    icon: User,
    color: 'bg-green-600',
    inputs: [],
    outputs: [
      { key: 'member', label: 'Member', type: 'user' },
      { key: 'guild', label: 'Guild', type: 'object' }
    ],
    configurable: false
  },

  // HTTP Requests
  {
    type: 'http_get',
    name: 'HTTP GET',
    description: 'Make a GET request to a URL with variable support',
    category: 'HTTP',
    icon: Download,
    color: 'bg-cyan-600',
    inputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' }
    ],
    outputs: [
      { key: 'response', label: 'Response', type: 'object' },
      { key: 'data', label: 'Data', type: 'any' },
      { key: 'status', label: 'Status', type: 'number' },
      { key: 'error', label: 'Error', type: 'text' }
    ],
    configurable: true,
    config: [
      {
        key: 'url',
        label: 'URL',
        type: 'text',
        placeholder: 'https://localhost:5001/api/bots/{{BOT_ID}}'
      },
      {
        key: 'headers',
        label: 'Headers (JSON)',
        type: 'textarea',
        placeholder: '{"Authorization": "Bearer {{TOKEN}}", "Content-Type": "application/json"}'
      }
    ]
  },

  {
    type: 'http_post',
    name: 'HTTP POST',
    description: 'Make a POST request to a URL with variable support',
    category: 'HTTP',
    icon: Upload,
    color: 'bg-indigo-600',
    inputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' },
      { key: 'data', label: 'Data', type: 'object' }
    ],
    outputs: [
      { key: 'response', label: 'Response', type: 'object' },
      { key: 'data', label: 'Data', type: 'any' },
      { key: 'status', label: 'Status', type: 'number' },
      { key: 'error', label: 'Error', type: 'text' }
    ],
    configurable: true,
    config: [
      {
        key: 'url',
        label: 'URL',
        type: 'text',
        placeholder: 'https://localhost:5001/api/bots/{{BOT_ID}}/start'
      },
      {
        key: 'body',
        label: 'Request Body (JSON)',
        type: 'textarea',
        placeholder: '{"message": "{{content}}", "userId": "{{author.id}}"}'
      },
      {
        key: 'headers',
        label: 'Headers (JSON)',
        type: 'textarea',
        placeholder: '{"Content-Type": "application/json", "Authorization": "Bearer {{TOKEN}}"}'
      }
    ]
  },

  {
    type: 'http_put',
    name: 'HTTP PUT',
    description: 'Make a PUT request to a URL with variable support',
    category: 'HTTP',
    icon: Settings,
    color: 'bg-violet-600',
    inputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' },
      { key: 'data', label: 'Data', type: 'object' }
    ],
    outputs: [
      { key: 'response', label: 'Response', type: 'object' },
      { key: 'data', label: 'Data', type: 'any' },
      { key: 'status', label: 'Status', type: 'number' },
      { key: 'error', label: 'Error', type: 'text' }
    ],
    configurable: true,
    config: [
      {
        key: 'url',
        label: 'URL',
        type: 'text',
        placeholder: 'https://localhost:5001/api/bots/{{BOT_ID}}'
      },
      {
        key: 'body',
        label: 'Request Body (JSON)',
        type: 'textarea',
        placeholder: '{"name": "{{BOT_NAME}}", "description": "Updated bot"}'
      },
      {
        key: 'headers',
        label: 'Headers (JSON)',
        type: 'textarea',
        placeholder: '{"Content-Type": "application/json", "Authorization": "Bearer {{TOKEN}}"}'
      }
    ]
  },

  {
    type: 'http_delete',
    name: 'HTTP DELETE',
    description: 'Make a DELETE request to a URL with variable support',
    category: 'HTTP',
    icon: X,
    color: 'bg-red-600',
    inputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' }
    ],
    outputs: [
      { key: 'response', label: 'Response', type: 'object' },
      { key: 'status', label: 'Status', type: 'number' },
      { key: 'success', label: 'Success', type: 'boolean' },
      { key: 'error', label: 'Error', type: 'text' }
    ],
    configurable: true,
    config: [
      {
        key: 'url',
        label: 'URL',
        type: 'text',
        placeholder: 'https://localhost:5001/api/bots/{{BOT_ID}}'
      },
      {
        key: 'headers',
        label: 'Headers (JSON)',
        type: 'textarea',
        placeholder: '{"Authorization": "Bearer {{TOKEN}}"}'
      }
    ]
  },

  // Discord Actions
  {
    type: 'send_message',
    name: 'Send Message',
    description: 'Send a message to a channel',
    category: 'Actions',
    icon: Send,
    color: 'bg-purple-600',
    inputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' },
      { key: 'channel', label: 'Channel', type: 'channel' },
      { key: 'content', label: 'Content', type: 'text', editable: true, placeholder: 'Hello {{author}}!' }
    ],
    outputs: [
      { key: 'message', label: 'Sent Message', type: 'message' },
      { key: 'success', label: 'Success', type: 'boolean' }
    ],
    configurable: true,
    config: [
      {
        key: 'content',
        label: 'Message Content',
        type: 'textarea',
        placeholder: 'Hello {{author}}! Welcome to the server.'
      }
    ]
  },

  {
    type: 'reply_message',
    name: 'Reply to Message',
    description: 'Reply to a message',
    category: 'Actions',
    icon: MessageSquare,
    color: 'bg-pink-600',
    inputs: [
      { key: 'message', label: 'Original Message', type: 'message' },
      { key: 'content', label: 'Reply Content', type: 'text', editable: true }
    ],
    outputs: [
      { key: 'reply', label: 'Reply Message', type: 'message' },
      { key: 'success', label: 'Success', type: 'boolean' }
    ],
    configurable: true,
    config: [
      {
        key: 'content',
        label: 'Reply Content',
        type: 'textarea',
        placeholder: 'Thanks for your message!'
      }
    ]
  },

  // Logic & Control
  {
    type: 'condition',
    name: 'Condition',
    description: 'Check a condition and branch execution',
    category: 'Logic',
    icon: GitBranch,
    color: 'bg-orange-600',
    inputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' },
      { key: 'value1', label: 'Value 1', type: 'any', editable: true },
      { key: 'value2', label: 'Value 2', type: 'any', editable: true }
    ],
    outputs: [
      { key: 'true', label: 'True', type: 'any' },
      { key: 'false', label: 'False', type: 'any' }
    ],
    configurable: true,
    config: [
      {
        key: 'operator',
        label: 'Operator',
        type: 'select',
        defaultValue: 'equals',
        options: [
          { label: 'Equals', value: 'equals' },
          { label: 'Not Equals', value: 'notEquals' },
          { label: 'Contains', value: 'contains' },
          { label: 'Starts With', value: 'startsWith' },
          { label: 'Greater Than', value: 'greaterThan' },
          { label: 'Less Than', value: 'lessThan' }
        ]
      }
    ]
  },

  {
    type: 'filter',
    name: 'Filter',
    description: 'Filter data based on conditions',
    category: 'Logic',
    icon: Filter,
    color: 'bg-yellow-600',
    inputs: [
      { key: 'data', label: 'Data', type: 'any' },
      { key: 'condition', label: 'Condition', type: 'text', editable: true }
    ],
    outputs: [
      { key: 'filtered', label: 'Filtered Data', type: 'any' },
      { key: 'count', label: 'Count', type: 'number' }
    ],
    configurable: true,
    config: [
      {
        key: 'property',
        label: 'Property to Filter',
        type: 'text',
        placeholder: 'username'
      },
      {
        key: 'value',
        label: 'Filter Value',
        type: 'text',
        placeholder: 'admin'
      }
    ]
  },

  // Utilities
  {
    type: 'delay',
    name: 'Delay',
    description: 'Add a delay before continuing',
    category: 'Utilities',
    icon: Clock,
    color: 'bg-gray-600',
    inputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' }
    ],
    outputs: [
      { key: 'continue', label: 'Continue', type: 'any' }
    ],
    configurable: true,
    config: [
      {
        key: 'duration',
        label: 'Duration (seconds)',
        type: 'number',
        defaultValue: '1'
      }
    ]
  },

  {
    type: 'variable_set',
    name: 'Set Variable',
    description: 'Set a variable value',
    category: 'Utilities',
    icon: Database,
    color: 'bg-teal-600',
    inputs: [
      { key: 'trigger', label: 'Trigger', type: 'any' },
      { key: 'value', label: 'Value', type: 'any', editable: true }
    ],
    outputs: [
      { key: 'variable', label: 'Variable', type: 'any' },
      { key: 'continue', label: 'Continue', type: 'any' }
    ],
    configurable: true,
    config: [
      {
        key: 'variableName',
        label: 'Variable Name',
        type: 'text',
        placeholder: 'myVariable'
      },
      {
        key: 'value',
        label: 'Value',
        type: 'text',
        placeholder: 'Hello World'
      }
    ]
  }
];

export function getCardDefinition(type: string): CardDefinition {
  return cardDefinitions.find(def => def.type === type) || cardDefinitions[0];
}

export function getCardsByCategory(): Record<string, CardDefinition[]> {
  return cardDefinitions.reduce((acc, card) => {
    if (!acc[card.category]) {
      acc[card.category] = [];
    }
    acc[card.category].push(card);
    return acc;
  }, {} as Record<string, CardDefinition[]>);
}
