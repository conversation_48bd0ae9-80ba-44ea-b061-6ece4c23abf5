interface CardInstance {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: Record<string, any>;
}

interface Connection {
  id: string;
  fromCardId: string;
  fromOutput: string;
  toCardId: string;
  toInput: string;
}

export function generateCodeFromFlow(
  cards: CardInstance[], 
  connections: Connection[], 
  variables: Record<string, string>
): string {
  let code = `// Generated code from Visual Builder\n\n`;
  
  // Add variables
  Object.entries(variables).forEach(([key, value]) => {
    code += `const ${key} = '${value}';\n`;
  });
  code += '\n';

  // Find event cards (cards with no inputs)
  const eventCards = cards.filter(card => {
    const hasInputConnections = connections.some(conn => conn.toCardId === card.id);
    return !hasInputConnections && ['message_received', 'member_join'].includes(card.type);
  });

  // Generate code for each event flow
  eventCards.forEach(eventCard => {
    code += generateEventFlow(eventCard, cards, connections, variables);
    code += '\n';
  });

  return code;
}

function generateEventFlow(
  eventCard: CardInstance,
  cards: CardInstance[],
  connections: Connection[],
  variables: Record<string, string>
): string {
  let code = '';

  switch (eventCard.type) {
    case 'message_received':
      code += generateMessageReceivedFlow(eventCard, cards, connections, variables);
      break;
    case 'member_join':
      code += generateMemberJoinFlow(eventCard, cards, connections, variables);
      break;
  }

  return code;
}

function generateMessageReceivedFlow(
  eventCard: CardInstance,
  cards: CardInstance[],
  connections: Connection[],
  variables: Record<string, string>
): string {
  let code = 'bot.onMessage((message) => {\n';
  
  // Add trigger condition if specified
  if (eventCard.data.trigger && eventCard.data.trigger !== 'all' && eventCard.data.triggerValue) {
    const triggerValue = replaceVariables(eventCard.data.triggerValue, variables);
    
    switch (eventCard.data.trigger) {
      case 'startsWith':
        code += `    if (!message.content.startsWith('${triggerValue}')) return;\n`;
        break;
      case 'contains':
        code += `    if (!message.content.includes('${triggerValue}')) return;\n`;
        break;
      case 'exact':
        code += `    if (message.content !== '${triggerValue}') return;\n`;
        break;
    }
    code += '\n';
  }

  // Generate code for connected cards
  const connectedCards = getConnectedCards(eventCard.id, connections, cards);
  connectedCards.forEach(card => {
    code += generateCardCode(card, cards, connections, variables, '    ');
  });

  code += '});\n';
  return code;
}

function generateMemberJoinFlow(
  eventCard: CardInstance,
  cards: CardInstance[],
  connections: Connection[],
  variables: Record<string, string>
): string {
  let code = 'bot.onMemberJoin((member) => {\n';
  
  const connectedCards = getConnectedCards(eventCard.id, connections, cards);
  connectedCards.forEach(card => {
    code += generateCardCode(card, cards, connections, variables, '    ');
  });

  code += '});\n';
  return code;
}

function generateCardCode(
  card: CardInstance,
  cards: CardInstance[],
  connections: Connection[],
  variables: Record<string, string>,
  indent: string = ''
): string {
  let code = '';

  switch (card.type) {
    case 'send_message':
      code += generateSendMessageCode(card, variables, indent);
      break;
    case 'reply_message':
      code += generateReplyMessageCode(card, variables, indent);
      break;
    case 'http_get':
      code += generateHttpGetCode(card, variables, indent);
      break;
    case 'http_post':
      code += generateHttpPostCode(card, variables, indent);
      break;
    case 'http_put':
      code += generateHttpPutCode(card, variables, indent);
      break;
    case 'http_delete':
      code += generateHttpDeleteCode(card, variables, indent);
      break;
    case 'condition':
      code += generateConditionCode(card, cards, connections, variables, indent);
      break;
    case 'delay':
      code += generateDelayCode(card, variables, indent);
      break;
    case 'variable_set':
      code += generateVariableSetCode(card, variables, indent);
      break;
  }

  return code;
}

function generateSendMessageCode(card: CardInstance, variables: Record<string, string>, indent: string): string {
  const content = replaceVariables(card.data.content || 'Hello!', variables);
  return `${indent}// Send message\n${indent}const channel = message.channel;\n${indent}await bot.sendMessage(channel, '${content}');\n\n`;
}

function generateReplyMessageCode(card: CardInstance, variables: Record<string, string>, indent: string): string {
  const content = replaceVariables(card.data.content || 'Thanks for your message!', variables);
  return `${indent}// Reply to message\n${indent}await message.reply('${content}');\n\n`;
}

function generateHttpGetCode(card: CardInstance, variables: Record<string, string>, indent: string): string {
  const url = replaceVariables(card.data.url || '', variables);
  const headers = card.data.headers ? JSON.parse(card.data.headers) : {};
  
  let code = `${indent}// HTTP GET request\n`;
  code += `${indent}try {\n`;
  code += `${indent}    const response = await fetch('${url}', {\n`;
  code += `${indent}        method: 'GET',\n`;
  if (Object.keys(headers).length > 0) {
    code += `${indent}        headers: ${JSON.stringify(headers, null, 8).replace(/\n/g, '\n' + indent)},\n`;
  }
  code += `${indent}    });\n`;
  code += `${indent}    const data = await response.json();\n`;
  code += `${indent}    console.log('GET response:', data);\n`;
  code += `${indent}} catch (error) {\n`;
  code += `${indent}    console.error('GET request failed:', error);\n`;
  code += `${indent}}\n\n`;
  
  return code;
}

function generateHttpPostCode(card: CardInstance, variables: Record<string, string>, indent: string): string {
  const url = replaceVariables(card.data.url || '', variables);
  const body = replaceVariables(card.data.body || '{}', variables);
  const headers = card.data.headers ? JSON.parse(card.data.headers) : { 'Content-Type': 'application/json' };
  
  let code = `${indent}// HTTP POST request\n`;
  code += `${indent}try {\n`;
  code += `${indent}    const response = await fetch('${url}', {\n`;
  code += `${indent}        method: 'POST',\n`;
  code += `${indent}        headers: ${JSON.stringify(headers, null, 8).replace(/\n/g, '\n' + indent)},\n`;
  code += `${indent}        body: JSON.stringify(${body})\n`;
  code += `${indent}    });\n`;
  code += `${indent}    const data = await response.json();\n`;
  code += `${indent}    console.log('POST response:', data);\n`;
  code += `${indent}} catch (error) {\n`;
  code += `${indent}    console.error('POST request failed:', error);\n`;
  code += `${indent}}\n\n`;
  
  return code;
}

function generateHttpPutCode(card: CardInstance, variables: Record<string, string>, indent: string): string {
  const url = replaceVariables(card.data.url || '', variables);
  const body = replaceVariables(card.data.body || '{}', variables);
  const headers = card.data.headers ? JSON.parse(card.data.headers) : { 'Content-Type': 'application/json' };
  
  let code = `${indent}// HTTP PUT request\n`;
  code += `${indent}try {\n`;
  code += `${indent}    const response = await fetch('${url}', {\n`;
  code += `${indent}        method: 'PUT',\n`;
  code += `${indent}        headers: ${JSON.stringify(headers, null, 8).replace(/\n/g, '\n' + indent)},\n`;
  code += `${indent}        body: JSON.stringify(${body})\n`;
  code += `${indent}    });\n`;
  code += `${indent}    const data = await response.json();\n`;
  code += `${indent}    console.log('PUT response:', data);\n`;
  code += `${indent}} catch (error) {\n`;
  code += `${indent}    console.error('PUT request failed:', error);\n`;
  code += `${indent}}\n\n`;
  
  return code;
}

function generateHttpDeleteCode(card: CardInstance, variables: Record<string, string>, indent: string): string {
  const url = replaceVariables(card.data.url || '', variables);
  const headers = card.data.headers ? JSON.parse(card.data.headers) : {};
  
  let code = `${indent}// HTTP DELETE request\n`;
  code += `${indent}try {\n`;
  code += `${indent}    const response = await fetch('${url}', {\n`;
  code += `${indent}        method: 'DELETE',\n`;
  if (Object.keys(headers).length > 0) {
    code += `${indent}        headers: ${JSON.stringify(headers, null, 8).replace(/\n/g, '\n' + indent)},\n`;
  }
  code += `${indent}    });\n`;
  code += `${indent}    console.log('DELETE response status:', response.status);\n`;
  code += `${indent}} catch (error) {\n`;
  code += `${indent}    console.error('DELETE request failed:', error);\n`;
  code += `${indent}}\n\n`;
  
  return code;
}

function generateConditionCode(
  card: CardInstance,
  cards: CardInstance[],
  connections: Connection[],
  variables: Record<string, string>,
  indent: string
): string {
  const operator = card.data.operator || 'equals';
  const value1 = replaceVariables(card.data.value1 || '', variables);
  const value2 = replaceVariables(card.data.value2 || '', variables);
  
  let condition = '';
  switch (operator) {
    case 'equals':
      condition = `${value1} === ${value2}`;
      break;
    case 'notEquals':
      condition = `${value1} !== ${value2}`;
      break;
    case 'contains':
      condition = `${value1}.includes(${value2})`;
      break;
    case 'startsWith':
      condition = `${value1}.startsWith(${value2})`;
      break;
    case 'greaterThan':
      condition = `${value1} > ${value2}`;
      break;
    case 'lessThan':
      condition = `${value1} < ${value2}`;
      break;
  }
  
  let code = `${indent}// Condition check\n`;
  code += `${indent}if (${condition}) {\n`;
  
  // Generate code for true branch
  const trueConnections = connections.filter(conn => conn.fromCardId === card.id && conn.fromOutput === 'true');
  trueConnections.forEach(conn => {
    const connectedCard = cards.find(c => c.id === conn.toCardId);
    if (connectedCard) {
      code += generateCardCode(connectedCard, cards, connections, variables, indent + '    ');
    }
  });
  
  code += `${indent}} else {\n`;
  
  // Generate code for false branch
  const falseConnections = connections.filter(conn => conn.fromCardId === card.id && conn.fromOutput === 'false');
  falseConnections.forEach(conn => {
    const connectedCard = cards.find(c => c.id === conn.toCardId);
    if (connectedCard) {
      code += generateCardCode(connectedCard, cards, connections, variables, indent + '    ');
    }
  });
  
  code += `${indent}}\n\n`;
  return code;
}

function generateDelayCode(card: CardInstance, variables: Record<string, string>, indent: string): string {
  const duration = parseInt(card.data.duration || '1') * 1000;
  return `${indent}// Delay\n${indent}await new Promise(resolve => setTimeout(resolve, ${duration}));\n\n`;
}

function generateVariableSetCode(card: CardInstance, variables: Record<string, string>, indent: string): string {
  const varName = card.data.variableName || 'myVariable';
  const value = replaceVariables(card.data.value || '', variables);
  return `${indent}// Set variable\n${indent}const ${varName} = '${value}';\n\n`;
}

function getConnectedCards(cardId: string, connections: Connection[], cards: CardInstance[]): CardInstance[] {
  const connectedCardIds = connections
    .filter(conn => conn.fromCardId === cardId)
    .map(conn => conn.toCardId);
  
  return cards.filter(card => connectedCardIds.includes(card.id));
}

function replaceVariables(text: string, variables: Record<string, string>): string {
  let result = text;
  
  // Replace template variables like {{BOT_ID}}
  Object.entries(variables).forEach(([key, value]) => {
    result = result.replace(new RegExp(`{{${key}}}`, 'g'), value);
  });
  
  // Replace common Discord.js variables
  result = result.replace(/{{author}}/g, '${message.author.username}');
  result = result.replace(/{{author\.id}}/g, '${message.author.id}');
  result = result.replace(/{{content}}/g, '${message.content}');
  result = result.replace(/{{channel}}/g, '${message.channel.name}');
  result = result.replace(/{{guild}}/g, '${message.guild.name}');
  
  return result;
}
