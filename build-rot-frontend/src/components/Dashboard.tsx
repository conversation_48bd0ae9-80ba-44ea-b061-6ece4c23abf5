'use client';

import { useState, useEffect } from 'react';
import { Plus, Bot, Play, Square, Settings, Code, LogOut, Blocks } from 'lucide-react';
import BotCard from './BotCard';
import AddBotModal from './AddBotModal';
import CodeEditor from './CodeEditor';
import VisualBuilder from './VisualBuilder';

interface User {
  id: string;
  email: string;
}

interface Bot {
  id: string;
  name: string;
  description: string;
  discord_username: string;
  status: 'active' | 'inactive';
  created_at: string;
}

interface DashboardProps {
  user: User;
  onLogout: () => void;
}

export default function Dashboard({ user, onLogout }: DashboardProps) {
  const [bots, setBots] = useState<Bot[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddBot, setShowAddBot] = useState(false);
  const [selectedBot, setSelectedBot] = useState<Bot | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'code' | 'visual'>('overview');

  useEffect(() => {
    fetchBots();
  }, []);

  const fetchBots = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:5000/api/bots', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setBots(data.bots);
      }
    } catch (error) {
      console.error('Failed to fetch bots:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    onLogout();
  };

  const handleBotAdded = (newBot: Bot) => {
    setBots([...bots, newBot]);
    setShowAddBot(false);
  };

  const handleBotUpdated = (updatedBot: Bot) => {
    setBots(bots.map(bot => bot.id === updatedBot.id ? updatedBot : bot));
    if (selectedBot?.id === updatedBot.id) {
      setSelectedBot(updatedBot);
    }
  };

  const handleBotDeleted = (botId: string) => {
    setBots(bots.filter(bot => bot.id !== botId));
    if (selectedBot?.id === botId) {
      setSelectedBot(null);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Bot className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">Build Rot</h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Welcome, {user.email}</span>
              <button
                onClick={handleLogout}
                className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900"
              >
                <LogOut className="h-4 w-4 mr-1" />
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {selectedBot ? (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setSelectedBot(null)}
                  className="text-blue-600 hover:text-blue-800"
                >
                  ← Back to Dashboard
                </button>
                <h2 className="text-2xl font-bold text-gray-900">{selectedBot.name}</h2>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  selectedBot.status === 'active' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {selectedBot.status}
                </span>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow">
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex">
                  <button
                    onClick={() => setActiveTab('overview')}
                    className={`py-2 px-4 border-b-2 font-medium text-sm ${
                      activeTab === 'overview'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    Overview
                  </button>
                  <button
                    onClick={() => setActiveTab('visual')}
                    className={`py-2 px-4 border-b-2 font-medium text-sm ${
                      activeTab === 'visual'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Blocks className="h-4 w-4 inline mr-1" />
                    Visual Builder
                  </button>
                  <button
                    onClick={() => setActiveTab('code')}
                    className={`py-2 px-4 border-b-2 font-medium text-sm ${
                      activeTab === 'code'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Code className="h-4 w-4 inline mr-1" />
                    Code Editor
                  </button>
                </nav>
              </div>

              <div className="p-6">
                {activeTab === 'overview' ? (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Bot Information</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Name</label>
                          <p className="mt-1 text-sm text-gray-900">{selectedBot.name}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Discord Username</label>
                          <p className="mt-1 text-sm text-gray-900">{selectedBot.discord_username}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Status</label>
                          <p className="mt-1 text-sm text-gray-900 capitalize">{selectedBot.status}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Created</label>
                          <p className="mt-1 text-sm text-gray-900">
                            {new Date(selectedBot.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </div>

                    {selectedBot.description && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Description</label>
                        <p className="mt-1 text-sm text-gray-900">{selectedBot.description}</p>
                      </div>
                    )}
                  </div>
                ) : activeTab === 'visual' ? (
                  <VisualBuilder
                    bot={selectedBot}
                    onBotUpdated={handleBotUpdated}
                  />
                ) : (
                  <CodeEditor
                    bot={selectedBot}
                    onBotUpdated={handleBotUpdated}
                  />
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">Your Discord Bots</h2>
              <button
                onClick={() => setShowAddBot(true)}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Bot
              </button>
            </div>

            {bots.length === 0 ? (
              <div className="text-center py-12">
                <Bot className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No bots yet</h3>
                <p className="text-gray-600 mb-4">Get started by adding your first Discord bot</p>
                <button
                  onClick={() => setShowAddBot(true)}
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Your First Bot
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {bots.map((bot) => (
                  <BotCard
                    key={bot.id}
                    bot={bot}
                    onSelect={() => setSelectedBot(bot)}
                    onUpdate={handleBotUpdated}
                    onDelete={handleBotDeleted}
                  />
                ))}
              </div>
            )}
          </div>
        )}
      </main>

      {showAddBot && (
        <AddBotModal
          onClose={() => setShowAddBot(false)}
          onBotAdded={handleBotAdded}
        />
      )}
    </div>
  );
}
