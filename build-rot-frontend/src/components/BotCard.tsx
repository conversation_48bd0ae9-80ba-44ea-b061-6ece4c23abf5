'use client';

import { useState } from 'react';
import { Bot, Play, Square, Settings, Trash2, MoreVertical } from 'lucide-react';

interface Bot {
  id: string;
  name: string;
  description: string;
  discord_username: string;
  status: 'active' | 'inactive';
  created_at: string;
}

interface BotCardProps {
  bot: Bot;
  onSelect: () => void;
  onUpdate: (bot: Bot) => void;
  onDelete: (botId: string) => void;
}

export default function BotCard({ bot, onSelect, onUpdate, onDelete }: BotCardProps) {
  const [loading, setLoading] = useState(false);
  const [showMenu, setShowMenu] = useState(false);

  const handleStatusToggle = async (e: React.MouseEvent) => {
    e.stopPropagation();
    setLoading(true);

    try {
      const token = localStorage.getItem('token');
      const action = bot.status === 'active' ? 'stop' : 'start';
      
      const response = await fetch(`http://localhost:5001/api/bots/${bot.id}/${action}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        onUpdate({
          ...bot,
          status: bot.status === 'active' ? 'inactive' : 'active'
        });
      }
    } catch (error) {
      console.error('Failed to toggle bot status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (!confirm('Are you sure you want to delete this bot? This action cannot be undone.')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:5001/api/bots/${bot.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        onDelete(bot.id);
      }
    } catch (error) {
      console.error('Failed to delete bot:', error);
    }
  };

  return (
    <div 
      onClick={onSelect}
      className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer relative"
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center">
          <div className="p-2 bg-blue-100 rounded-lg mr-3">
            <Bot className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{bot.name}</h3>
            <p className="text-sm text-gray-600">@{bot.discord_username}</p>
          </div>
        </div>
        
        <div className="relative">
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowMenu(!showMenu);
            }}
            className="p-1 text-gray-400 hover:text-gray-600"
          >
            <MoreVertical className="h-4 w-4" />
          </button>
          
          {showMenu && (
            <div className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
              <button
                onClick={handleDelete}
                className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Bot
              </button>
            </div>
          )}
        </div>
      </div>

      {bot.description && (
        <p className="text-sm text-gray-600 mb-4 line-clamp-2">{bot.description}</p>
      )}

      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            bot.status === 'active' 
              ? 'bg-green-100 text-green-800' 
              : 'bg-gray-100 text-gray-800'
          }`}>
            {bot.status}
          </span>
        </div>

        <button
          onClick={handleStatusToggle}
          disabled={loading}
          className={`flex items-center px-3 py-1 rounded-md text-sm font-medium transition-colors ${
            bot.status === 'active'
              ? 'bg-red-100 text-red-700 hover:bg-red-200'
              : 'bg-green-100 text-green-700 hover:bg-green-200'
          } disabled:opacity-50`}
        >
          {loading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-1"></div>
          ) : bot.status === 'active' ? (
            <Square className="h-4 w-4 mr-1" />
          ) : (
            <Play className="h-4 w-4 mr-1" />
          )}
          {bot.status === 'active' ? 'Stop' : 'Start'}
        </button>
      </div>

      <div className="mt-4 pt-4 border-t border-gray-100">
        <p className="text-xs text-gray-500">
          Created {new Date(bot.created_at).toLocaleDateString()}
        </p>
      </div>

      {showMenu && (
        <div 
          className="fixed inset-0 z-0" 
          onClick={(e) => {
            e.stopPropagation();
            setShowMenu(false);
          }}
        />
      )}
    </div>
  );
}
