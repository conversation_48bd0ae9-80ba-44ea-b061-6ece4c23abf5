'use client';

interface Connection {
  id: string;
  fromCardId: string;
  fromOutput: string;
  toCardId: string;
  toInput: string;
}

interface CardInstance {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: Record<string, any>;
}

interface ConnectionLineProps {
  connection: Connection;
  cards: CardInstance[];
}

export default function ConnectionLine({ connection, cards }: ConnectionLineProps) {
  const fromCard = cards.find(c => c.id === connection.fromCardId);
  const toCard = cards.find(c => c.id === connection.toCardId);

  if (!fromCard || !toCard) return null;

  // Calculate connection points (simplified - you'd want more precise positioning)
  const fromX = fromCard.position.x + 192; // Card width
  const fromY = fromCard.position.y + 60;  // Approximate output position
  const toX = toCard.position.x;
  const toY = toCard.position.y + 60;      // Approximate input position

  // Create a curved path
  const midX = (fromX + toX) / 2;
  const pathData = `M ${fromX} ${fromY} C ${midX} ${fromY}, ${midX} ${toY}, ${toX} ${toY}`;

  return (
    <svg 
      className="absolute inset-0 pointer-events-none"
      style={{ zIndex: 0 }}
    >
      <defs>
        <marker
          id={`arrowhead-${connection.id}`}
          markerWidth="10"
          markerHeight="7"
          refX="9"
          refY="3.5"
          orient="auto"
        >
          <polygon
            points="0 0, 10 3.5, 0 7"
            fill="#6b7280"
          />
        </marker>
      </defs>
      <path
        d={pathData}
        stroke="#6b7280"
        strokeWidth="2"
        fill="none"
        markerEnd={`url(#arrowhead-${connection.id})`}
      />
    </svg>
  );
}
