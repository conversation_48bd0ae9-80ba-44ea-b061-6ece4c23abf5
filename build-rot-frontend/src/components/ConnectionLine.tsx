'use client';

interface Connection {
  id: string;
  fromCardId: string;
  fromOutput: string;
  toCardId: string;
  toInput: string;
}

interface CardInstance {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: Record<string, any>;
}

interface ConnectionLineProps {
  connection: Connection;
  cards: CardInstance[];
}

export default function ConnectionLine({ connection, cards }: ConnectionLineProps) {
  const fromCard = cards.find(c => c.id === connection.fromCardId);
  const toCard = cards.find(c => c.id === connection.toCardId);

  if (!fromCard || !toCard) return null;

  // Calculate connection points with better positioning
  const cardWidth = 192;
  const cardHeight = 120; // Approximate card height

  const fromX = fromCard.position.x + cardWidth;
  const fromY = fromCard.position.y + cardHeight / 2;
  const toX = toCard.position.x;
  const toY = toCard.position.y + cardHeight / 2;

  // Create a curved path with better curve
  const controlPointOffset = Math.abs(toX - fromX) * 0.5;
  const pathData = `M ${fromX} ${fromY} C ${fromX + controlPointOffset} ${fromY}, ${toX - controlPointOffset} ${toY}, ${toX} ${toY}`;

  return (
    <svg
      className="absolute inset-0 pointer-events-none"
      style={{ zIndex: 0 }}
    >
      <defs>
        <marker
          id={`arrowhead-${connection.id}`}
          markerWidth="10"
          markerHeight="7"
          refX="9"
          refY="3.5"
          orient="auto"
        >
          <polygon
            points="0 0, 10 3.5, 0 7"
            fill="#3b82f6"
          />
        </marker>
      </defs>
      <path
        d={pathData}
        stroke="#3b82f6"
        strokeWidth="2"
        fill="none"
        markerEnd={`url(#arrowhead-${connection.id})`}
        className="drop-shadow-sm"
      />
    </svg>
  );
}
