'use client';

import { useState } from 'react';
import { X, Circle, Square } from 'lucide-react';
import { getCardDefinition } from '../lib/cardDefinitions';

interface CardInstance {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: Record<string, any>;
}

interface BuilderCardProps {
  card: CardInstance;
  isSelected: boolean;
  onMouseDown: (e: React.MouseEvent) => void;
  onUpdate: (data: Record<string, any>) => void;
  onDelete: () => void;
  onStartConnection: (cardId: string, output: string, outputType: string) => void;
  onCompleteConnection: (cardId: string, input: string, inputType: string) => void;
  connecting: { cardId: string; output: string; outputType: string } | null;
  variables: Record<string, string>;
}

export default function BuilderCard({
  card,
  isSelected,
  onMouseDown,
  onUpdate,
  onDelete,
  onStartConnection,
  onCompleteConnection,
  connecting,
  variables
}: BuilderCardProps) {
  const [isEditing, setIsEditing] = useState(false);
  const cardDef = getCardDefinition(card.type);

  const handleInputChange = (inputKey: string, value: string) => {
    onUpdate({ [inputKey]: value });
  };

  const handleOutputClick = (output: any) => {
    if (connecting) return;
    onStartConnection(card.id, output.key, output.type);
  };

  const handleInputClick = (input: any) => {
    if (!connecting) return;
    onCompleteConnection(card.id, input.key, input.type);
  };

  const replaceVariables = (text: string) => {
    let result = text;
    Object.entries(variables).forEach(([key, value]) => {
      result = result.replace(new RegExp(`{{${key}}}`, 'g'), value);
    });
    return result;
  };

  const getTypeColor = (type: string) => {
    const colors = {
      text: 'bg-blue-500',
      number: 'bg-green-500',
      boolean: 'bg-purple-500',
      object: 'bg-orange-500',
      array: 'bg-red-500',
      any: 'bg-gray-500',
      message: 'bg-indigo-500',
      user: 'bg-pink-500',
      channel: 'bg-teal-500',
      embed: 'bg-yellow-500',
      http: 'bg-cyan-500'
    };
    return colors[type as keyof typeof colors] || 'bg-gray-500';
  };

  return (
    <div
      className={`absolute bg-white rounded-lg shadow-lg border-2 min-w-48 ${
        isSelected ? 'border-blue-500' : 'border-gray-200'
      } ${connecting?.cardId === card.id ? 'ring-2 ring-blue-300' : ''}`}
      style={{
        left: card.position.x,
        top: card.position.y,
        zIndex: isSelected ? 10 : 1
      }}
      onMouseDown={onMouseDown}
    >
      {/* Header */}
      <div className={`px-3 py-2 rounded-t-lg ${cardDef.color} text-white flex items-center justify-between`}>
        <div className="flex items-center">
          <cardDef.icon className="h-4 w-4 mr-2" />
          <span className="font-medium text-sm">{cardDef.name}</span>
        </div>
        <button
          onClick={(e) => {
            e.stopPropagation();
            onDelete();
          }}
          className="text-white hover:text-red-200"
        >
          <X className="h-4 w-4" />
        </button>
      </div>

      {/* Inputs */}
      {cardDef.inputs.length > 0 && (
        <div className="px-3 py-2 border-b">
          <div className="text-xs font-medium text-gray-600 mb-2">Inputs</div>
          {cardDef.inputs.map((input) => (
            <div key={input.key} className="flex items-center mb-2 last:mb-0">
              <button
                className={`w-3 h-3 rounded-full mr-2 border-2 border-white ${getTypeColor(input.type)} ${
                  connecting && connecting.outputType === input.type ? 'ring-2 ring-blue-300' : ''
                }`}
                onClick={() => handleInputClick(input)}
                title={`${input.label} (${input.type})`}
              >
                <Circle className="w-full h-full" />
              </button>
              <div className="flex-1">
                <div className="text-xs text-gray-700">{input.label}</div>
                {input.editable && (
                  <input
                    type={input.inputType || 'text'}
                    value={card.data[input.key] || input.defaultValue || ''}
                    onChange={(e) => handleInputChange(input.key, e.target.value)}
                    placeholder={input.placeholder}
                    className="w-full text-xs border rounded px-1 py-0.5 mt-1"
                    onClick={(e) => e.stopPropagation()}
                  />
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Content/Configuration */}
      {cardDef.configurable && (
        <div className="px-3 py-2 border-b">
          <div className="text-xs font-medium text-gray-600 mb-2">Configuration</div>
          {cardDef.config?.map((configItem) => (
            <div key={configItem.key} className="mb-2 last:mb-0">
              <label className="text-xs text-gray-700 block mb-1">{configItem.label}</label>
              {configItem.type === 'select' ? (
                <select
                  value={card.data[configItem.key] || configItem.defaultValue || ''}
                  onChange={(e) => handleInputChange(configItem.key, e.target.value)}
                  className="w-full text-xs border rounded px-1 py-0.5"
                  onClick={(e) => e.stopPropagation()}
                >
                  {configItem.options?.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              ) : configItem.type === 'textarea' ? (
                <textarea
                  value={card.data[configItem.key] || configItem.defaultValue || ''}
                  onChange={(e) => handleInputChange(configItem.key, e.target.value)}
                  placeholder={configItem.placeholder}
                  className="w-full text-xs border rounded px-1 py-0.5 resize-none"
                  rows={3}
                  onClick={(e) => e.stopPropagation()}
                />
              ) : (
                <input
                  type={configItem.inputType || 'text'}
                  value={card.data[configItem.key] || configItem.defaultValue || ''}
                  onChange={(e) => handleInputChange(configItem.key, e.target.value)}
                  placeholder={configItem.placeholder}
                  className="w-full text-xs border rounded px-1 py-0.5"
                  onClick={(e) => e.stopPropagation()}
                />
              )}
              {configItem.key === 'url' && card.data[configItem.key] && (
                <div className="text-xs text-gray-500 mt-1">
                  Preview: {replaceVariables(card.data[configItem.key])}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Outputs */}
      {cardDef.outputs.length > 0 && (
        <div className="px-3 py-2">
          <div className="text-xs font-medium text-gray-600 mb-2">Outputs</div>
          {cardDef.outputs.map((output) => (
            <div key={output.key} className="flex items-center justify-end mb-2 last:mb-0">
              <div className="flex-1 text-right">
                <div className="text-xs text-gray-700">{output.label}</div>
              </div>
              <button
                className={`w-3 h-3 rounded-full ml-2 border-2 border-white ${getTypeColor(output.type)}`}
                onClick={() => handleOutputClick(output)}
                title={`${output.label} (${output.type})`}
              >
                <Square className="w-full h-full" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Status indicator */}
      {card.data.status && (
        <div className="px-3 py-1 bg-gray-50 rounded-b-lg">
          <div className="text-xs text-gray-600">
            Status: <span className="font-medium">{card.data.status}</span>
          </div>
        </div>
      )}
    </div>
  );
}
