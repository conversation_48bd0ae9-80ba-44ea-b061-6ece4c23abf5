'use client';

import { useState } from 'react';
import { X, Search } from 'lucide-react';
import { getCardsByCategory, CardDefinition } from '../lib/cardDefinitions';

interface CardLibraryProps {
  onAddCard: (cardType: string, position: { x: number; y: number }) => void;
  onClose: () => void;
}

export default function CardLibrary({ onAddCard, onClose }: CardLibraryProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  
  const cardsByCategory = getCardsByCategory();
  const categories = ['all', ...Object.keys(cardsByCategory)];

  const filteredCards = Object.entries(cardsByCategory).reduce((acc, [category, cards]) => {
    if (selectedCategory !== 'all' && category !== selectedCategory) return acc;
    
    const filtered = cards.filter(card =>
      card.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      card.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    if (filtered.length > 0) {
      acc[category] = filtered;
    }
    
    return acc;
  }, {} as Record<string, CardDefinition[]>);

  const handleCardClick = (cardType: string) => {
    // Add card at center of viewport
    onAddCard(cardType, { x: 300, y: 200 });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] flex flex-col">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">Card Library</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 border-b">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search cards..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-6">
          {Object.keys(filteredCards).length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">No cards found matching your search.</p>
            </div>
          ) : (
            <div className="space-y-6">
              {Object.entries(filteredCards).map(([category, cards]) => (
                <div key={category}>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">{category}</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {cards.map((card) => (
                      <button
                        key={card.type}
                        onClick={() => handleCardClick(card.type)}
                        className="text-left p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all"
                      >
                        <div className="flex items-center mb-2">
                          <div className={`p-2 rounded-md ${card.color} text-white mr-3`}>
                            <card.icon className="h-4 w-4" />
                          </div>
                          <h4 className="font-medium text-gray-900">{card.name}</h4>
                        </div>
                        <p className="text-sm text-gray-600 mb-3">{card.description}</p>
                        
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <div className="flex items-center space-x-2">
                            {card.inputs.length > 0 && (
                              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                {card.inputs.length} input{card.inputs.length !== 1 ? 's' : ''}
                              </span>
                            )}
                            {card.outputs.length > 0 && (
                              <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                                {card.outputs.length} output{card.outputs.length !== 1 ? 's' : ''}
                              </span>
                            )}
                          </div>
                          {card.configurable && (
                            <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded">
                              Configurable
                            </span>
                          )}
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="p-6 border-t bg-gray-50">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div>
              <strong>Tip:</strong> Drag cards from here to your canvas, then connect matching input/output types
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-blue-500 rounded-full mr-1"></div>
                <span>Input</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full mr-1"></div>
                <span>Output</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
