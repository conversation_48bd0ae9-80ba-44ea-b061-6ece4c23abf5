'use client';

import { useState, useRef, useCallback } from 'react';
import { Plus, Save, Download } from 'lucide-react';

interface Connection {
  id: string;
  fromCardId: string;
  fromOutput: string;
  toCardId: string;
  toInput: string;
}

interface CardInstance {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: Record<string, any>;
}

interface Bot {
  id: string;
  name: string;
  description: string;
  discord_username: string;
  status: 'active' | 'inactive';
  created_at: string;
}

interface VisualBuilderProps {
  bot: Bot;
  onBotUpdated: (bot: Bot) => void;
}

export default function VisualBuilder({ bot, onBotUpdated }: VisualBuilderProps) {
  const [zoom, setZoom] = useState(1);
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 });

  const canvasRef = useRef<HTMLDivElement>(null);

  const resetView = useCallback(() => {
    setZoom(1);
    setPanOffset({ x: 0, y: 0 });
  }, []);

  const handleWheel = useCallback((e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    const newZoom = Math.max(0.1, Math.min(3, zoom * delta));
    setZoom(newZoom);
  }, [zoom]);

  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between p-4 border-b bg-white">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-medium">Visual Bot Builder</h3>
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <span>Zoom: {Math.round(zoom * 100)}%</span>
            <button
              onClick={resetView}
              className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded"
            >
              Reset View
            </button>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Card
          </button>
          <button
            className="flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            <Save className="h-4 w-4 mr-1" />
            Save
          </button>
          <button
            className="flex items-center px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            <Download className="h-4 w-4 mr-1" />
            Export Code
          </button>
        </div>
      </div>

      <div className="flex-1 flex">
        <div className="flex-1 relative overflow-hidden">
          <div
            ref={canvasRef}
            className="w-full h-full bg-gray-50 relative cursor-grab"
            onWheel={handleWheel}
          >
            <div
              className="absolute inset-0 opacity-20"
              style={{
                backgroundImage: `
                  linear-gradient(to right, #e5e7eb 1px, transparent 1px),
                  linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
                `,
                backgroundSize: `${20 * zoom}px ${20 * zoom}px`,
                backgroundPosition: `${panOffset.x}px ${panOffset.y}px`
              }}
            />

            <div
              className="absolute inset-0"
              style={{
                transform: `translate(${panOffset.x}px, ${panOffset.y}px) scale(${zoom})`,
                transformOrigin: '0 0'
              }}
            >
              <div className="p-8 text-center">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Enhanced Visual Builder</h3>
                <p className="text-gray-600 mb-4">
                  Zoom: {Math.round(zoom * 100)}% | Pan: ({Math.round(panOffset.x)}, {Math.round(panOffset.y)})
                </p>
                <p className="text-sm text-gray-500">
                  Use mouse wheel to zoom • Alt + click to pan • Larger canvas area for building
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="w-80 bg-white border-l p-4 overflow-y-auto">
          <div className="mb-4">
            <h4 className="font-medium mb-2">Controls</h4>
            <div className="text-xs text-gray-600 space-y-1">
              <div>• Mouse wheel: Zoom in/out</div>
              <div>• Alt + Click: Pan canvas</div>
              <div>• Larger canvas area</div>
            </div>
          </div>

          <div className="mt-6">
            <h4 className="font-medium mb-3">Available Variables</h4>
            <div className="text-xs text-gray-600 space-y-1">
              <div><code>{'{{BOT_ID}}'}</code> - Bot ID</div>
              <div><code>{'{{BOT_NAME}}'}</code> - Bot Name</div>
              <div><code>{'{{author}}'}</code> - Message author</div>
              <div><code>{'{{content}}'}</code> - Message content</div>
              <div><code>{'{{channel}}'}</code> - Channel name</div>
              <div><code>{'{{member.username}}'}</code> - Member username</div>
              <div><code>{'{{member.id}}'}</code> - Member ID</div>
              <div><code>{'{{guild.name}}'}</code> - Server name</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
