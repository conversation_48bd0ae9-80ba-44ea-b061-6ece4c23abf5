'use client';

import { useState, useRef, useCallback } from 'react';
import { Plus, Play, Save, Download, Upload } from 'lucide-react';
import BuilderCard from './BuilderCard';
import ConnectionLine from './ConnectionLine';
import CardLibrary from './CardLibrary';
import { generateCodeFromFlow } from '../lib/codeGenerator';

interface Connection {
  id: string;
  fromCardId: string;
  fromOutput: string;
  toCardId: string;
  toInput: string;
}

interface CardInstance {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: Record<string, any>;
}

interface Bot {
  id: string;
  name: string;
  description: string;
  discord_username: string;
  status: 'active' | 'inactive';
  created_at: string;
}

interface VisualBuilderProps {
  bot: Bot;
  onBotUpdated: (bot: Bot) => void;
}

export default function VisualBuilder({ bot, onBotUpdated }: VisualBuilderProps) {
  const [cards, setCards] = useState<CardInstance[]>([]);
  const [connections, setConnections] = useState<Connection[]>([]);
  const [selectedCard, setSelectedCard] = useState<string | null>(null);
  const [draggedCard, setDraggedCard] = useState<string | null>(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [showLibrary, setShowLibrary] = useState(false);
  const [connecting, setConnecting] = useState<{
    cardId: string;
    output: string;
    outputType: string;
  } | null>(null);
  const [variables, setVariables] = useState<Record<string, string>>({
    BOT_ID: bot.id,
    BOT_NAME: bot.name,
    SERVER_URL: 'http://localhost:5000'
  });

  const canvasRef = useRef<HTMLDivElement>(null);

  const addCard = useCallback((cardType: string, position: { x: number; y: number }) => {
    const newCard: CardInstance = {
      id: `card_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: cardType,
      position,
      data: {}
    };
    setCards(prev => [...prev, newCard]);
    setShowLibrary(false);
  }, []);

  const updateCard = useCallback((cardId: string, data: Record<string, any>) => {
    setCards(prev => prev.map(card => 
      card.id === cardId ? { ...card, data: { ...card.data, ...data } } : card
    ));
  }, []);

  const deleteCard = useCallback((cardId: string) => {
    setCards(prev => prev.filter(card => card.id !== cardId));
    setConnections(prev => prev.filter(conn => 
      conn.fromCardId !== cardId && conn.toCardId !== cardId
    ));
  }, []);

  const startConnection = useCallback((cardId: string, output: string, outputType: string) => {
    setConnecting({ cardId, output, outputType });
  }, []);

  const completeConnection = useCallback((toCardId: string, toInput: string, inputType: string) => {
    if (!connecting) return;
    
    if (connecting.outputType === inputType || inputType === 'any' || connecting.outputType === 'any') {
      const newConnection: Connection = {
        id: `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        fromCardId: connecting.cardId,
        fromOutput: connecting.output,
        toCardId,
        toInput
      };
      
      setConnections(prev => [
        ...prev.filter(conn => !(conn.toCardId === toCardId && conn.toInput === toInput)),
        newConnection
      ]);
    }
    
    setConnecting(null);
  }, [connecting]);

  const handleCardMouseDown = useCallback((e: React.MouseEvent, cardId: string) => {
    if (e.button !== 0) return;
    
    const card = cards.find(c => c.id === cardId);
    if (!card) return;

    const rect = e.currentTarget.getBoundingClientRect();
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
    setDraggedCard(cardId);
    setSelectedCard(cardId);
  }, [cards]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!draggedCard || !canvasRef.current) return;

    const canvasRect = canvasRef.current.getBoundingClientRect();
    const newPosition = {
      x: e.clientX - canvasRect.left - dragOffset.x,
      y: e.clientY - canvasRect.top - dragOffset.y
    };

    setCards(prev => prev.map(card =>
      card.id === draggedCard ? { ...card, position: newPosition } : card
    ));
  }, [draggedCard, dragOffset]);

  const handleMouseUp = useCallback(() => {
    setDraggedCard(null);
  }, []);

  const generateCode = useCallback(() => {
    return generateCodeFromFlow(cards, connections, variables);
  }, [cards, connections, variables]);

  const saveFlow = useCallback(async () => {
    const flowData = {
      cards,
      connections,
      variables
    };

    try {
      const token = localStorage.getItem('token');
      await fetch(`http://localhost:5000/api/code/${bot.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ 
          code: generateCode(),
          language: 'javascript',
          flowData: JSON.stringify(flowData)
        }),
      });
      console.log('Flow saved successfully');
    } catch (error) {
      console.error('Failed to save flow:', error);
    }
  }, [cards, connections, variables, bot.id, generateCode]);

  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between p-4 border-b bg-white">
        <h3 className="text-lg font-medium">Visual Bot Builder</h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowLibrary(true)}
            className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Card
          </button>
          <button
            onClick={saveFlow}
            className="flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            <Save className="h-4 w-4 mr-1" />
            Save
          </button>
          <button
            onClick={() => {
              const code = generateCode();
              navigator.clipboard.writeText(code);
            }}
            className="flex items-center px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            <Download className="h-4 w-4 mr-1" />
            Export Code
          </button>
        </div>
      </div>

      <div className="flex-1 flex">
        <div className="flex-1 relative overflow-hidden">
          <div
            ref={canvasRef}
            className="w-full h-full bg-gray-50 relative"
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onClick={() => {
              if (!connecting) {
                setSelectedCard(null);
              }
            }}
          >
            {/* Grid background */}
            <div 
              className="absolute inset-0 opacity-20"
              style={{
                backgroundImage: `
                  linear-gradient(to right, #e5e7eb 1px, transparent 1px),
                  linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
                `,
                backgroundSize: '20px 20px'
              }}
            />

            {/* Connection lines */}
            {connections.map(connection => (
              <ConnectionLine
                key={connection.id}
                connection={connection}
                cards={cards}
              />
            ))}

            {/* Cards */}
            {cards.map(card => (
              <BuilderCard
                key={card.id}
                card={card}
                isSelected={selectedCard === card.id}
                onMouseDown={(e) => handleCardMouseDown(e, card.id)}
                onUpdate={(data) => updateCard(card.id, data)}
                onDelete={() => deleteCard(card.id)}
                onStartConnection={startConnection}
                onCompleteConnection={completeConnection}
                connecting={connecting}
                variables={variables}
              />
            ))}

            {/* Connection preview */}
            {connecting && (
              <div className="absolute inset-0 pointer-events-none">
                <svg className="w-full h-full">
                  <defs>
                    <marker
                      id="arrowhead-preview"
                      markerWidth="10"
                      markerHeight="7"
                      refX="9"
                      refY="3.5"
                      orient="auto"
                    >
                      <polygon
                        points="0 0, 10 3.5, 0 7"
                        fill="#3b82f6"
                      />
                    </marker>
                  </defs>
                </svg>
              </div>
            )}
          </div>
        </div>

        {/* Variables panel */}
        <div className="w-80 bg-white border-l p-4">
          <h4 className="font-medium mb-3">Variables</h4>
          <div className="space-y-2">
            {Object.entries(variables).map(([key, value]) => (
              <div key={key} className="flex items-center space-x-2">
                <span className="text-sm font-mono text-gray-600 w-20">{key}:</span>
                <input
                  type="text"
                  value={value}
                  onChange={(e) => setVariables(prev => ({ ...prev, [key]: e.target.value }))}
                  className="flex-1 px-2 py-1 text-sm border rounded"
                />
              </div>
            ))}
            <button
              onClick={() => {
                const newKey = prompt('Variable name:');
                if (newKey && !variables[newKey]) {
                  setVariables(prev => ({ ...prev, [newKey]: '' }));
                }
              }}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              + Add Variable
            </button>
          </div>

          {selectedCard && (
            <div className="mt-6">
              <h4 className="font-medium mb-3">Card Properties</h4>
              <div className="text-sm text-gray-600">
                Selected: {cards.find(c => c.id === selectedCard)?.type}
              </div>
            </div>
          )}
        </div>
      </div>

      {showLibrary && (
        <CardLibrary
          onAddCard={addCard}
          onClose={() => setShowLibrary(false)}
        />
      )}
    </div>
  );
}
