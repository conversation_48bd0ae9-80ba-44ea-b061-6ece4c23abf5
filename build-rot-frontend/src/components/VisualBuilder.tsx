'use client';

import { useState, useRef, useCallback } from 'react';
import { Plus, Play, Save, Download, Upload } from 'lucide-react';
import BuilderCard from './BuilderCard';
import ConnectionLine from './ConnectionLine';
import CardLibrary from './CardLibrary';
import { generateCodeFromFlow } from '../lib/codeGenerator';

interface Connection {
  id: string;
  fromCardId: string;
  fromOutput: string;
  toCardId: string;
  toInput: string;
}

interface CardInstance {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: Record<string, any>;
}

interface Bot {
  id: string;
  name: string;
  description: string;
  discord_username: string;
  status: 'active' | 'inactive';
  created_at: string;
}

interface VisualBuilderProps {
  bot: Bot;
  onBotUpdated: (bot: Bot) => void;
}

export default function VisualBuilder({ bot, onBotUpdated }: VisualBuilderProps) {
  const [cards, setCards] = useState<CardInstance[]>([]);
  const [connections, setConnections] = useState<Connection[]>([]);
  const [selectedCard, setSelectedCard] = useState<string | null>(null);
  const [draggedCard, setDraggedCard] = useState<string | null>(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [showLibrary, setShowLibrary] = useState(false);
  const [connecting, setConnecting] = useState<{
    cardId: string;
    output: string;
    outputType: string;
  } | null>(null);
  const [variables, setVariables] = useState<Record<string, string>>({
    BOT_ID: bot.id,
    BOT_NAME: bot.name,
    SERVER_URL: 'http://localhost:5001'
  });
  const [zoom, setZoom] = useState(1);
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 });
  const [isPanning, setIsPanning] = useState(false);
  const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 });

  const canvasRef = useRef<HTMLDivElement>(null);

  const addCard = useCallback((cardType: string, position: { x: number; y: number }) => {
    // Adjust position for zoom and pan
    const adjustedPosition = {
      x: (position.x - panOffset.x) / zoom,
      y: (position.y - panOffset.y) / zoom
    };

    const newCard: CardInstance = {
      id: `card_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: cardType,
      position: adjustedPosition,
      data: {}
    };
    setCards(prev => [...prev, newCard]);
    setShowLibrary(false);
  }, [zoom, panOffset]);

  const updateCard = useCallback((cardId: string, data: Record<string, any>) => {
    setCards(prev => prev.map(card => 
      card.id === cardId ? { ...card, data: { ...card.data, ...data } } : card
    ));
  }, []);

  const deleteCard = useCallback((cardId: string) => {
    setCards(prev => prev.filter(card => card.id !== cardId));
    setConnections(prev => prev.filter(conn => 
      conn.fromCardId !== cardId && conn.toCardId !== cardId
    ));
  }, []);

  const startConnection = useCallback((cardId: string, output: string, outputType: string) => {
    setConnecting({ cardId, output, outputType });
  }, []);

  const completeConnection = useCallback((toCardId: string, toInput: string, inputType: string) => {
    if (!connecting) return;
    
    if (connecting.outputType === inputType || inputType === 'any' || connecting.outputType === 'any') {
      const newConnection: Connection = {
        id: `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        fromCardId: connecting.cardId,
        fromOutput: connecting.output,
        toCardId,
        toInput
      };
      
      setConnections(prev => [
        ...prev.filter(conn => !(conn.toCardId === toCardId && conn.toInput === toInput)),
        newConnection
      ]);
    }
    
    setConnecting(null);
  }, [connecting]);

  const handleCardMouseDown = useCallback((e: React.MouseEvent, cardId: string) => {
    if (e.button !== 0) return;
    
    const card = cards.find(c => c.id === cardId);
    if (!card) return;

    const rect = e.currentTarget.getBoundingClientRect();
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
    setDraggedCard(cardId);
    setSelectedCard(cardId);
  }, [cards]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isPanning) {
      const deltaX = e.clientX - lastPanPoint.x;
      const deltaY = e.clientY - lastPanPoint.y;

      setPanOffset(prev => ({
        x: prev.x + deltaX,
        y: prev.y + deltaY
      }));

      setLastPanPoint({ x: e.clientX, y: e.clientY });
      return;
    }

    if (!draggedCard || !canvasRef.current) return;

    const canvasRect = canvasRef.current.getBoundingClientRect();
    const newPosition = {
      x: (e.clientX - canvasRect.left - dragOffset.x - panOffset.x) / zoom,
      y: (e.clientY - canvasRect.top - dragOffset.y - panOffset.y) / zoom
    };

    setCards(prev => prev.map(card =>
      card.id === draggedCard ? { ...card, position: newPosition } : card
    ));
  }, [draggedCard, dragOffset, isPanning, lastPanPoint, zoom, panOffset]);

  const handleMouseUp = useCallback(() => {
    setDraggedCard(null);
    setIsPanning(false);
  }, []);

  const handleCanvasMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.button === 1 || (e.button === 0 && e.altKey)) { // Middle mouse or Alt+Left click for panning
      e.preventDefault();
      setIsPanning(true);
      setLastPanPoint({ x: e.clientX, y: e.clientY });
    }
  }, []);

  const handleWheel = useCallback((e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    const newZoom = Math.max(0.1, Math.min(3, zoom * delta));
    setZoom(newZoom);
  }, [zoom]);

  const resetView = useCallback(() => {
    setZoom(1);
    setPanOffset({ x: 0, y: 0 });
  }, []);

  const generateCode = useCallback(() => {
    return generateCodeFromFlow(cards, connections, variables);
  }, [cards, connections, variables]);

  const saveFlow = useCallback(async () => {
    const flowData = {
      cards,
      connections,
      variables
    };

    try {
      const token = localStorage.getItem('token');
      await fetch(`http://localhost:5001/api/code/${bot.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ 
          code: generateCode(),
          language: 'javascript',
          flowData: JSON.stringify(flowData)
        }),
      });
      console.log('Flow saved successfully');
    } catch (error) {
      console.error('Failed to save flow:', error);
    }
  }, [cards, connections, variables, bot.id, generateCode]);

  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between p-4 border-b bg-white">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-medium">Visual Bot Builder</h3>
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <span>Zoom: {Math.round(zoom * 100)}%</span>
            <button
              onClick={resetView}
              className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded"
            >
              Reset View
            </button>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowLibrary(true)}
            className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Card
          </button>
          <button
            onClick={saveFlow}
            className="flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            <Save className="h-4 w-4 mr-1" />
            Save
          </button>
          <button
            onClick={() => {
              const code = generateCode();
              navigator.clipboard.writeText(code);
            }}
            className="flex items-center px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            <Download className="h-4 w-4 mr-1" />
            Export Code
          </button>
        </div>
      </div>

      <div className="flex-1 flex">
        <div className="flex-1 relative overflow-hidden">
          <div
            ref={canvasRef}
            className="w-full h-full bg-gray-50 relative cursor-grab active:cursor-grabbing"
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseDown={handleCanvasMouseDown}
            onWheel={handleWheel}
            onClick={() => {
              if (!connecting) {
                setSelectedCard(null);
              }
            }}
            style={{ cursor: isPanning ? 'grabbing' : 'grab' }}
          >
            {/* Grid background */}
            <div
              className="absolute inset-0 opacity-20"
              style={{
                backgroundImage: `
                  linear-gradient(to right, #e5e7eb 1px, transparent 1px),
                  linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
                `,
                backgroundSize: `${20 * zoom}px ${20 * zoom}px`,
                backgroundPosition: `${panOffset.x}px ${panOffset.y}px`,
                transform: `scale(${zoom})`,
                transformOrigin: '0 0'
              }}
            />

            {/* Canvas content container */}
            <div
              className="absolute inset-0"
              style={{
                transform: `translate(${panOffset.x}px, ${panOffset.y}px) scale(${zoom})`,
                transformOrigin: '0 0'
              }}
            >

              {/* Connection lines */}
              {connections.map(connection => (
                <ConnectionLine
                  key={connection.id}
                  connection={connection}
                  cards={cards}
                />
              ))}

              {/* Cards */}
              {cards.map(card => (
                <BuilderCard
                  key={card.id}
                  card={card}
                  isSelected={selectedCard === card.id}
                  onMouseDown={(e) => handleCardMouseDown(e, card.id)}
                  onUpdate={(data) => updateCard(card.id, data)}
                  onDelete={() => deleteCard(card.id)}
                  onStartConnection={startConnection}
                  onCompleteConnection={completeConnection}
                  connecting={connecting}
                  variables={variables}
                />
              ))}
            </div>

            {/* Connection preview */}
            {connecting && (
              <div className="absolute inset-0 pointer-events-none">
                <svg className="w-full h-full">
                  <defs>
                    <marker
                      id="arrowhead-preview"
                      markerWidth="10"
                      markerHeight="7"
                      refX="9"
                      refY="3.5"
                      orient="auto"
                    >
                      <polygon
                        points="0 0, 10 3.5, 0 7"
                        fill="#3b82f6"
                      />
                    </marker>
                  </defs>
                </svg>
              </div>
            )}
          </div>
        </div>

        {/* Variables panel */}
        <div className="w-80 bg-white border-l p-4 overflow-y-auto">
          <div className="mb-4">
            <h4 className="font-medium mb-2">Controls</h4>
            <div className="text-xs text-gray-600 space-y-1">
              <div>• Mouse wheel: Zoom in/out</div>
              <div>• Alt + Click: Pan canvas</div>
              <div>• Middle click: Pan canvas</div>
            </div>
          </div>

          <h4 className="font-medium mb-3">Variables</h4>
          <div className="space-y-2">
            {Object.entries(variables).map(([key, value]) => (
              <div key={key} className="flex items-center space-x-2">
                <span className="text-sm font-mono text-gray-600 w-20">{key}:</span>
                <input
                  type="text"
                  value={value}
                  onChange={(e) => setVariables(prev => ({ ...prev, [key]: e.target.value }))}
                  className="flex-1 px-2 py-1 text-sm border rounded"
                />
              </div>
            ))}
            <button
              onClick={() => {
                const newKey = prompt('Variable name:');
                if (newKey && !variables[newKey]) {
                  setVariables(prev => ({ ...prev, [newKey]: '' }));
                }
              }}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              + Add Variable
            </button>
          </div>

          {selectedCard && (
            <div className="mt-6">
              <h4 className="font-medium mb-3">Card Properties</h4>
              <div className="text-sm text-gray-600">
                Selected: {cards.find(c => c.id === selectedCard)?.type}
              </div>
            </div>
          )}

          <div className="mt-6">
            <h4 className="font-medium mb-3">Available Variables</h4>
            <div className="text-xs text-gray-600 space-y-1">
              <div><code>{{`{BOT_ID}`}}</code> - Bot ID</div>
              <div><code>{{`{BOT_NAME}`}}</code> - Bot Name</div>
              <div><code>{{`{author}`}}</code> - Message author</div>
              <div><code>{{`{content}`}}</code> - Message content</div>
              <div><code>{{`{channel}`}}</code> - Channel name</div>
              <div><code>{{`{member.username}`}}</code> - Member username</div>
              <div><code>{{`{member.id}`}}</code> - Member ID</div>
              <div><code>{{`{guild.name}`}}</code> - Server name</div>
            </div>
          </div>
        </div>
      </div>

      {showLibrary && (
        <CardLibrary
          onAddCard={addCard}
          onClose={() => setShowLibrary(false)}
        />
      )}
    </div>
  );
}
