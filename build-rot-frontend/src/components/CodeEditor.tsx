'use client';

import { useState, useEffect } from 'react';
import { Editor } from '@monaco-editor/react';
import { Play, Save, FileText, Zap } from 'lucide-react';

interface Bot {
  id: string;
  name: string;
  description: string;
  discord_username: string;
  status: 'active' | 'inactive';
  created_at: string;
}

interface CodeEditorProps {
  bot: Bot;
  onBotUpdated: (bot: Bot) => void;
}

const defaultCode = `// Welcome to Bot Rot! 
// This is a simple example to get you started.

// Basic message response
bot.onMessage((message) => {
    if (message.content === '!hello') {
        message.reply('Hello there! 👋');
    }
    
    if (message.content === '!ping') {
        message.reply(\`Pong! My ping is \${bot.client.ws.ping}ms\`);
    }
});

// Slash command example
bot.command('userinfo', 'Get information about a user', [
    { name: 'user', description: 'The user to get info about', type: 'user', required: false }
])
.run(async (interaction) => {
    const user = interaction.options.getUser('user') || interaction.user;
    const embed = bot.embed(
        \`User Info: \${user.username}\`,
        \`**ID:** \${user.id}\\n**Created:** \${user.createdAt.toDateString()}\`,
        '#00ff00'
    );
    await bot.reply(interaction, '', { embed });
});

// Welcome new members
bot.onMemberJoin((member) => {
    const channel = member.guild.channels.cache.find(ch => ch.name === 'welcome');
    if (channel) {
        const embed = bot.embed(
            'New Member!',
            \`Welcome \${member.user.username} to the server! 🎉\`,
            '#00ff00'
        );
        bot.sendMessage(channel, '', { embed });
    }
});

// Register commands when bot is ready
bot.onReady(() => {
    console.log('Bot is ready!');
    bot.registerCommands(); // Register slash commands globally
});`;

const examples = [
  {
    name: 'Basic Commands',
    description: 'Simple message responses and ping command',
    code: `bot.onMessage((message) => {
    if (message.content === '!hello') {
        message.reply('Hello there! 👋');
    }
    
    if (message.content === '!ping') {
        message.reply(\`Pong! My ping is \${bot.client.ws.ping}ms\`);
    }
});`
  },
  {
    name: 'Slash Commands',
    description: 'Modern Discord slash commands',
    code: `bot.command('greet', 'Greet a user', [
    { name: 'user', description: 'User to greet', type: 'user', required: true }
])
.run(async (interaction) => {
    const user = interaction.options.getUser('user');
    await bot.reply(interaction, \`Hello \${user.username}! 👋\`);
});

bot.onReady(() => {
    bot.registerCommands();
});`
  },
  {
    name: 'Embeds & Buttons',
    description: 'Rich embeds with interactive buttons',
    code: `bot.command('welcome', 'Send a welcome message with buttons')
.run(async (interaction) => {
    const embed = bot.embed(
        'Welcome!',
        'Click a button below to get started',
        '#ff9900'
    );
    
    const helpButton = bot.button('Get Help', 'help_button', 'Primary');
    const communityButton = bot.button('Join Community', 'community_button', 'Success');
    const row = bot.row(helpButton, communityButton);
    
    await bot.reply(interaction, '', { embed, components: [row] });
});`
  }
];

export default function CodeEditor({ bot, onBotUpdated }: CodeEditorProps) {
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [deploying, setDeploying] = useState(false);
  const [validating, setValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<{ valid: boolean; error?: string } | null>(null);

  useEffect(() => {
    fetchCode();
  }, [bot.id]);

  const fetchCode = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:5001/api/code/${bot.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCode(data.code || defaultCode);
      }
    } catch (error) {
      console.error('Failed to fetch code:', error);
      setCode(defaultCode);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:5001/api/code/${bot.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ code, language: 'javascript' }),
      });

      if (response.ok) {
        console.log('Code saved successfully');
      }
    } catch (error) {
      console.error('Failed to save code:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleValidate = async () => {
    setValidating(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:5001/api/code/${bot.id}/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ code }),
      });

      if (response.ok) {
        const result = await response.json();
        setValidationResult(result);
      }
    } catch (error) {
      console.error('Failed to validate code:', error);
    } finally {
      setValidating(false);
    }
  };

  const handleDeploy = async () => {
    setDeploying(true);
    try {
      const token = localStorage.getItem('token');
      
      // Save code first
      await fetch(`http://localhost:5001/api/code/${bot.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ code, language: 'javascript' }),
      });

      // Deploy bot
      const response = await fetch(`http://localhost:5001/api/code/${bot.id}/deploy`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        onBotUpdated({ ...bot, status: 'active' });
        console.log('Bot deployed successfully');
      }
    } catch (error) {
      console.error('Failed to deploy bot:', error);
    } finally {
      setDeploying(false);
    }
  };

  const insertExample = (example: typeof examples[0]) => {
    setCode(example.code);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleSave}
            disabled={saving}
            className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            <Save className="h-4 w-4 mr-1" />
            {saving ? 'Saving...' : 'Save'}
          </button>
          
          <button
            onClick={handleValidate}
            disabled={validating}
            className="flex items-center px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50"
          >
            <FileText className="h-4 w-4 mr-1" />
            {validating ? 'Validating...' : 'Validate'}
          </button>
          
          <button
            onClick={handleDeploy}
            disabled={deploying}
            className="flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
          >
            <Zap className="h-4 w-4 mr-1" />
            {deploying ? 'Deploying...' : 'Deploy Bot'}
          </button>
        </div>
      </div>

      {validationResult && (
        <div className={`p-3 rounded-md ${
          validationResult.valid 
            ? 'bg-green-100 border border-green-400 text-green-700'
            : 'bg-red-100 border border-red-400 text-red-700'
        }`}>
          {validationResult.valid ? '✅ Code syntax is valid' : `❌ ${validationResult.error}`}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
        <div className="lg:col-span-3">
          <div className="border border-gray-300 rounded-md overflow-hidden">
            <Editor
              height="500px"
              defaultLanguage="javascript"
              value={code}
              onChange={(value) => setCode(value || '')}
              theme="vs-dark"
              options={{
                minimap: { enabled: false },
                fontSize: 14,
                lineNumbers: 'on',
                roundedSelection: false,
                scrollBeyondLastLine: false,
                automaticLayout: true,
              }}
            />
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Examples</h3>
            <div className="space-y-2">
              {examples.map((example, index) => (
                <button
                  key={index}
                  onClick={() => insertExample(example)}
                  className="w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-md border"
                >
                  <div className="font-medium text-sm text-gray-900">{example.name}</div>
                  <div className="text-xs text-gray-600 mt-1">{example.description}</div>
                </button>
              ))}
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <h4 className="text-sm font-medium text-blue-900 mb-2">Bot Rot API</h4>
            <div className="text-xs text-blue-800 space-y-1">
              <div><code>bot.onMessage(callback)</code></div>
              <div><code>bot.onReady(callback)</code></div>
              <div><code>bot.command(name, desc, options)</code></div>
              <div><code>bot.embed(title, desc, color)</code></div>
              <div><code>bot.button(label, id, style)</code></div>
              <div><code>bot.reply(interaction, content)</code></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
