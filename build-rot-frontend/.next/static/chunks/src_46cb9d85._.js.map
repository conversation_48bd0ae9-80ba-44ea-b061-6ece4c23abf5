{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TFE/build-rot/build-rot-frontend/src/components/AuthForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface AuthFormProps {\n  onLogin: (user: any) => void;\n}\n\nexport default function AuthForm({ onLogin }: AuthFormProps) {\n  const [isLogin, setIsLogin] = useState(true);\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const endpoint = isLogin ? '/api/auth/login' : '/api/auth/register';\n      const response = await fetch(`http://localhost:5001${endpoint}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, password }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        if (isLogin) {\n          localStorage.setItem('token', data.token);\n          onLogin(data.user);\n        } else {\n          setIsLogin(true);\n          setError('Registration successful! Please login.');\n        }\n      } else {\n        setError(data.error || 'Something went wrong');\n      }\n    } catch (err) {\n      setError('Network error. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"max-w-md w-full space-y-8 p-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">Build Rot</h1>\n          <p className=\"text-gray-600\">Discord Bot Builder Platform</p>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-lg p-8\">\n          <div className=\"text-center mb-6\">\n            <h2 className=\"text-2xl font-semibold text-gray-900\">\n              {isLogin ? 'Sign In' : 'Create Account'}\n            </h2>\n            <p className=\"text-gray-600 mt-2\">\n              {isLogin ? 'Welcome back!' : 'Join the bot building community'}\n            </p>\n          </div>\n\n          {error && (\n            <div className=\"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\">\n              {error}\n            </div>\n          )}\n\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email Address\n              </label>\n              <input\n                id=\"email\"\n                type=\"email\"\n                required\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Enter your email\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                type=\"password\"\n                required\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Enter your password\"\n                minLength={6}\n              />\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? (\n                <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"></div>\n              ) : (\n                isLogin ? 'Sign In' : 'Create Account'\n              )}\n            </button>\n          </form>\n\n          <div className=\"mt-6 text-center\">\n            <button\n              onClick={() => {\n                setIsLogin(!isLogin);\n                setError('');\n              }}\n              className=\"text-blue-600 hover:text-blue-500 text-sm\"\n            >\n              {isLogin ? \"Don't have an account? Sign up\" : \"Already have an account? Sign in\"}\n            </button>\n          </div>\n        </div>\n\n        <div className=\"text-center text-sm text-gray-500\">\n          <p>Build powerful Discord bots with ease</p>\n          <p>No coding experience required!</p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAQe,SAAS,SAAS,EAAE,OAAO,EAAiB;;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,UAAU,oBAAoB;YAC/C,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,UAAU,EAAE;gBAC/D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAS;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,IAAI,SAAS;oBACX,aAAa,OAAO,CAAC,SAAS,KAAK,KAAK;oBACxC,QAAQ,KAAK,IAAI;gBACnB,OAAO;oBACL,WAAW;oBACX,SAAS;gBACX;YACF,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAG/B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,UAAU,YAAY;;;;;;8CAEzB,6LAAC;oCAAE,WAAU;8CACV,UAAU,kBAAkB;;;;;;;;;;;;wBAIhC,uBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,QAAQ;4CACR,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,QAAQ;4CACR,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,WAAU;4CACV,aAAY;4CACZ,WAAW;;;;;;;;;;;;8CAIf,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,wBACC,6LAAC;wCAAI,WAAU;;;;;+CAEf,UAAU,YAAY;;;;;;;;;;;;sCAK5B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;oCACP,WAAW,CAAC;oCACZ,SAAS;gCACX;gCACA,WAAU;0CAET,UAAU,mCAAmC;;;;;;;;;;;;;;;;;8BAKpD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAE;;;;;;sCACH,6LAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAKb;GAnIwB;KAAA", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TFE/build-rot/build-rot-frontend/src/components/BotCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Bot, Play, Square, Settings, Trash2, MoreVertical } from 'lucide-react';\n\ninterface Bot {\n  id: string;\n  name: string;\n  description: string;\n  discord_username: string;\n  status: 'active' | 'inactive';\n  created_at: string;\n}\n\ninterface BotCardProps {\n  bot: Bot;\n  onSelect: () => void;\n  onUpdate: (bot: Bot) => void;\n  onDelete: (botId: string) => void;\n}\n\nexport default function BotCard({ bot, onSelect, onUpdate, onDelete }: BotCardProps) {\n  const [loading, setLoading] = useState(false);\n  const [showMenu, setShowMenu] = useState(false);\n\n  const handleStatusToggle = async (e: React.MouseEvent) => {\n    e.stopPropagation();\n    setLoading(true);\n\n    try {\n      const token = localStorage.getItem('token');\n      const action = bot.status === 'active' ? 'stop' : 'start';\n      \n      const response = await fetch(`http://localhost:5001/api/bots/${bot.id}/${action}`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        onUpdate({\n          ...bot,\n          status: bot.status === 'active' ? 'inactive' : 'active'\n        });\n      }\n    } catch (error) {\n      console.error('Failed to toggle bot status:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDelete = async (e: React.MouseEvent) => {\n    e.stopPropagation();\n    \n    if (!confirm('Are you sure you want to delete this bot? This action cannot be undone.')) {\n      return;\n    }\n\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch(`http://localhost:5001/api/bots/${bot.id}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        onDelete(bot.id);\n      }\n    } catch (error) {\n      console.error('Failed to delete bot:', error);\n    }\n  };\n\n  return (\n    <div \n      onClick={onSelect}\n      className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer relative\"\n    >\n      <div className=\"flex items-start justify-between mb-4\">\n        <div className=\"flex items-center\">\n          <div className=\"p-2 bg-blue-100 rounded-lg mr-3\">\n            <Bot className=\"h-6 w-6 text-blue-600\" />\n          </div>\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">{bot.name}</h3>\n            <p className=\"text-sm text-gray-600\">@{bot.discord_username}</p>\n          </div>\n        </div>\n        \n        <div className=\"relative\">\n          <button\n            onClick={(e) => {\n              e.stopPropagation();\n              setShowMenu(!showMenu);\n            }}\n            className=\"p-1 text-gray-400 hover:text-gray-600\"\n          >\n            <MoreVertical className=\"h-4 w-4\" />\n          </button>\n          \n          {showMenu && (\n            <div className=\"absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10\">\n              <button\n                onClick={handleDelete}\n                className=\"w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center\"\n              >\n                <Trash2 className=\"h-4 w-4 mr-2\" />\n                Delete Bot\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {bot.description && (\n        <p className=\"text-sm text-gray-600 mb-4 line-clamp-2\">{bot.description}</p>\n      )}\n\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center\">\n          <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n            bot.status === 'active' \n              ? 'bg-green-100 text-green-800' \n              : 'bg-gray-100 text-gray-800'\n          }`}>\n            {bot.status}\n          </span>\n        </div>\n\n        <button\n          onClick={handleStatusToggle}\n          disabled={loading}\n          className={`flex items-center px-3 py-1 rounded-md text-sm font-medium transition-colors ${\n            bot.status === 'active'\n              ? 'bg-red-100 text-red-700 hover:bg-red-200'\n              : 'bg-green-100 text-green-700 hover:bg-green-200'\n          } disabled:opacity-50`}\n        >\n          {loading ? (\n            <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-1\"></div>\n          ) : bot.status === 'active' ? (\n            <Square className=\"h-4 w-4 mr-1\" />\n          ) : (\n            <Play className=\"h-4 w-4 mr-1\" />\n          )}\n          {bot.status === 'active' ? 'Stop' : 'Start'}\n        </button>\n      </div>\n\n      <div className=\"mt-4 pt-4 border-t border-gray-100\">\n        <p className=\"text-xs text-gray-500\">\n          Created {new Date(bot.created_at).toLocaleDateString()}\n        </p>\n      </div>\n\n      {showMenu && (\n        <div \n          className=\"fixed inset-0 z-0\" \n          onClick={(e) => {\n            e.stopPropagation();\n            setShowMenu(false);\n          }}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAqBe,SAAS,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAgB;;IACjF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,qBAAqB,OAAO;QAChC,EAAE,eAAe;QACjB,WAAW;QAEX,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,SAAS,IAAI,MAAM,KAAK,WAAW,SAAS;YAElD,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE;gBACjF,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS;oBACP,GAAG,GAAG;oBACN,QAAQ,IAAI,MAAM,KAAK,WAAW,aAAa;gBACjD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,eAAe;QAEjB,IAAI,CAAC,QAAQ,4EAA4E;YACvF;QACF;QAEA,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,IAAI,EAAE,EAAE,EAAE;gBACvE,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS,IAAI,EAAE;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAEjB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAuC,IAAI,IAAI;;;;;;kDAC7D,6LAAC;wCAAE,WAAU;;4CAAwB;4CAAE,IAAI,gBAAgB;;;;;;;;;;;;;;;;;;;kCAI/D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB,YAAY,CAAC;gCACf;gCACA,WAAU;0CAEV,cAAA,6LAAC,6NAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;4BAGzB,0BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;YAQ5C,IAAI,WAAW,kBACd,6LAAC;gBAAE,WAAU;0BAA2C,IAAI,WAAW;;;;;;0BAGzE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAW,CAAC,2CAA2C,EAC3D,IAAI,MAAM,KAAK,WACX,gCACA,6BACJ;sCACC,IAAI,MAAM;;;;;;;;;;;kCAIf,6LAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAW,CAAC,6EAA6E,EACvF,IAAI,MAAM,KAAK,WACX,6CACA,iDACL,oBAAoB,CAAC;;4BAErB,wBACC,6LAAC;gCAAI,WAAU;;;;;uCACb,IAAI,MAAM,KAAK,yBACjB,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;qDAElB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAEjB,IAAI,MAAM,KAAK,WAAW,SAAS;;;;;;;;;;;;;0BAIxC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;wBAAwB;wBAC1B,IAAI,KAAK,IAAI,UAAU,EAAE,kBAAkB;;;;;;;;;;;;YAIvD,0BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,CAAC;oBACR,EAAE,eAAe;oBACjB,YAAY;gBACd;;;;;;;;;;;;AAKV;GArJwB;KAAA", "debugId": null}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TFE/build-rot/build-rot-frontend/src/components/AddBotModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { X } from 'lucide-react';\n\ninterface Bot {\n  id: string;\n  name: string;\n  description: string;\n  discord_username: string;\n  status: 'active' | 'inactive';\n  created_at: string;\n}\n\ninterface AddBotModalProps {\n  onClose: () => void;\n  onBotAdded: (bot: Bot) => void;\n}\n\nexport default function AddBotModal({ onClose, onBotAdded }: AddBotModalProps) {\n  const [name, setName] = useState('');\n  const [description, setDescription] = useState('');\n  const [token, setToken] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const authToken = localStorage.getItem('token');\n      const response = await fetch('http://localhost:5001/api/bots', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${authToken}`\n        },\n        body: JSON.stringify({ name, description, token }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        onBotAdded(data.bot);\n      } else {\n        setError(data.error || 'Failed to add bot');\n      }\n    } catch (err) {\n      setError('Network error. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-md w-full mx-4\">\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">Add Discord Bot</h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"p-6 space-y-4\">\n          {error && (\n            <div className=\"p-3 bg-red-100 border border-red-400 text-red-700 rounded\">\n              {error}\n            </div>\n          )}\n\n          <div>\n            <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Bot Name *\n            </label>\n            <input\n              id=\"name\"\n              type=\"text\"\n              required\n              value={name}\n              onChange={(e) => setName(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"My Awesome Bot\"\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Description\n            </label>\n            <textarea\n              id=\"description\"\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"What does your bot do?\"\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"token\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Discord Bot Token *\n            </label>\n            <input\n              id=\"token\"\n              type=\"password\"\n              required\n              value={token}\n              onChange={(e) => setToken(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"Your bot token from Discord Developer Portal\"\n            />\n            <p className=\"mt-1 text-xs text-gray-500\">\n              Get your bot token from the{' '}\n              <a \n                href=\"https://discord.com/developers/applications\" \n                target=\"_blank\" \n                rel=\"noopener noreferrer\"\n                className=\"text-blue-600 hover:text-blue-800\"\n              >\n                Discord Developer Portal\n              </a>\n            </p>\n          </div>\n\n          <div className=\"flex justify-end space-x-3 pt-4\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? (\n                <div className=\"flex items-center\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Adding...\n                </div>\n              ) : (\n                'Add Bot'\n              )}\n            </button>\n          </div>\n        </form>\n\n        <div className=\"px-6 pb-6\">\n          <div className=\"bg-blue-50 border border-blue-200 rounded-md p-4\">\n            <h3 className=\"text-sm font-medium text-blue-900 mb-2\">How to get your bot token:</h3>\n            <ol className=\"text-xs text-blue-800 space-y-1\">\n              <li>1. Go to Discord Developer Portal</li>\n              <li>2. Create a new application or select existing one</li>\n              <li>3. Go to \"Bot\" section</li>\n              <li>4. Copy the bot token</li>\n              <li>5. Make sure your bot has the necessary permissions</li>\n            </ol>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAmBe,SAAS,YAAY,EAAE,OAAO,EAAE,UAAU,EAAoB;;IAC3E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,YAAY,aAAa,OAAO,CAAC;YACvC,MAAM,WAAW,MAAM,MAAM,kCAAkC;gBAC7D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,WAAW;gBACxC;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM;oBAAa;gBAAM;YAClD;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW,KAAK,GAAG;YACrB,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;wBACrC,uBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAO,WAAU;8CAA+C;;;;;;8CAG/E,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAQ;oCACR,OAAO;oCACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oCACvC,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAc,WAAU;8CAA+C;;;;;;8CAGtF,6LAAC;oCACC,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA+C;;;;;;8CAGhF,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAQ;oCACR,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,WAAU;oCACV,aAAY;;;;;;8CAEd,6LAAC;oCAAE,WAAU;;wCAA6B;wCACZ;sDAC5B,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAML,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,wBACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;4CAAuE;;;;;;+CAIxF;;;;;;;;;;;;;;;;;;8BAMR,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GAxJwB;KAAA", "debugId": null}}, {"offset": {"line": 925, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TFE/build-rot/build-rot-frontend/src/components/CodeEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Editor } from '@monaco-editor/react';\nimport { Play, Save, FileText, Zap } from 'lucide-react';\n\ninterface Bot {\n  id: string;\n  name: string;\n  description: string;\n  discord_username: string;\n  status: 'active' | 'inactive';\n  created_at: string;\n}\n\ninterface CodeEditorProps {\n  bot: Bot;\n  onBotUpdated: (bot: Bot) => void;\n}\n\nconst defaultCode = `// Welcome to Bot Rot! \n// This is a simple example to get you started.\n\n// Basic message response\nbot.onMessage((message) => {\n    if (message.content === '!hello') {\n        message.reply('Hello there! 👋');\n    }\n    \n    if (message.content === '!ping') {\n        message.reply(\\`Pong! My ping is \\${bot.client.ws.ping}ms\\`);\n    }\n});\n\n// Slash command example\nbot.command('userinfo', 'Get information about a user', [\n    { name: 'user', description: 'The user to get info about', type: 'user', required: false }\n])\n.run(async (interaction) => {\n    const user = interaction.options.getUser('user') || interaction.user;\n    const embed = bot.embed(\n        \\`User Info: \\${user.username}\\`,\n        \\`**ID:** \\${user.id}\\\\n**Created:** \\${user.createdAt.toDateString()}\\`,\n        '#00ff00'\n    );\n    await bot.reply(interaction, '', { embed });\n});\n\n// Welcome new members\nbot.onMemberJoin((member) => {\n    const channel = member.guild.channels.cache.find(ch => ch.name === 'welcome');\n    if (channel) {\n        const embed = bot.embed(\n            'New Member!',\n            \\`Welcome \\${member.user.username} to the server! 🎉\\`,\n            '#00ff00'\n        );\n        bot.sendMessage(channel, '', { embed });\n    }\n});\n\n// Register commands when bot is ready\nbot.onReady(() => {\n    console.log('Bot is ready!');\n    bot.registerCommands(); // Register slash commands globally\n});`;\n\nconst examples = [\n  {\n    name: 'Basic Commands',\n    description: 'Simple message responses and ping command',\n    code: `bot.onMessage((message) => {\n    if (message.content === '!hello') {\n        message.reply('Hello there! 👋');\n    }\n    \n    if (message.content === '!ping') {\n        message.reply(\\`Pong! My ping is \\${bot.client.ws.ping}ms\\`);\n    }\n});`\n  },\n  {\n    name: 'Slash Commands',\n    description: 'Modern Discord slash commands',\n    code: `bot.command('greet', 'Greet a user', [\n    { name: 'user', description: 'User to greet', type: 'user', required: true }\n])\n.run(async (interaction) => {\n    const user = interaction.options.getUser('user');\n    await bot.reply(interaction, \\`Hello \\${user.username}! 👋\\`);\n});\n\nbot.onReady(() => {\n    bot.registerCommands();\n});`\n  },\n  {\n    name: 'Embeds & Buttons',\n    description: 'Rich embeds with interactive buttons',\n    code: `bot.command('welcome', 'Send a welcome message with buttons')\n.run(async (interaction) => {\n    const embed = bot.embed(\n        'Welcome!',\n        'Click a button below to get started',\n        '#ff9900'\n    );\n    \n    const helpButton = bot.button('Get Help', 'help_button', 'Primary');\n    const communityButton = bot.button('Join Community', 'community_button', 'Success');\n    const row = bot.row(helpButton, communityButton);\n    \n    await bot.reply(interaction, '', { embed, components: [row] });\n});`\n  }\n];\n\nexport default function CodeEditor({ bot, onBotUpdated }: CodeEditorProps) {\n  const [code, setCode] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [deploying, setDeploying] = useState(false);\n  const [validating, setValidating] = useState(false);\n  const [validationResult, setValidationResult] = useState<{ valid: boolean; error?: string } | null>(null);\n\n  useEffect(() => {\n    fetchCode();\n  }, [bot.id]);\n\n  const fetchCode = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch(`http://localhost:5001/api/code/${bot.id}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setCode(data.code || defaultCode);\n      }\n    } catch (error) {\n      console.error('Failed to fetch code:', error);\n      setCode(defaultCode);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSave = async () => {\n    setSaving(true);\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch(`http://localhost:5001/api/code/${bot.id}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify({ code, language: 'javascript' }),\n      });\n\n      if (response.ok) {\n        console.log('Code saved successfully');\n      }\n    } catch (error) {\n      console.error('Failed to save code:', error);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleValidate = async () => {\n    setValidating(true);\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch(`http://localhost:5001/api/code/${bot.id}/validate`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify({ code }),\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        setValidationResult(result);\n      }\n    } catch (error) {\n      console.error('Failed to validate code:', error);\n    } finally {\n      setValidating(false);\n    }\n  };\n\n  const handleDeploy = async () => {\n    setDeploying(true);\n    try {\n      const token = localStorage.getItem('token');\n      \n      // Save code first\n      await fetch(`http://localhost:5001/api/code/${bot.id}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify({ code, language: 'javascript' }),\n      });\n\n      // Deploy bot\n      const response = await fetch(`http://localhost:5001/api/code/${bot.id}/deploy`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        onBotUpdated({ ...bot, status: 'active' });\n        console.log('Bot deployed successfully');\n      }\n    } catch (error) {\n      console.error('Failed to deploy bot:', error);\n    } finally {\n      setDeploying(false);\n    }\n  };\n\n  const insertExample = (example: typeof examples[0]) => {\n    setCode(example.code);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-96\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <button\n            onClick={handleSave}\n            disabled={saving}\n            className=\"flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\"\n          >\n            <Save className=\"h-4 w-4 mr-1\" />\n            {saving ? 'Saving...' : 'Save'}\n          </button>\n          \n          <button\n            onClick={handleValidate}\n            disabled={validating}\n            className=\"flex items-center px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50\"\n          >\n            <FileText className=\"h-4 w-4 mr-1\" />\n            {validating ? 'Validating...' : 'Validate'}\n          </button>\n          \n          <button\n            onClick={handleDeploy}\n            disabled={deploying}\n            className=\"flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50\"\n          >\n            <Zap className=\"h-4 w-4 mr-1\" />\n            {deploying ? 'Deploying...' : 'Deploy Bot'}\n          </button>\n        </div>\n      </div>\n\n      {validationResult && (\n        <div className={`p-3 rounded-md ${\n          validationResult.valid \n            ? 'bg-green-100 border border-green-400 text-green-700'\n            : 'bg-red-100 border border-red-400 text-red-700'\n        }`}>\n          {validationResult.valid ? '✅ Code syntax is valid' : `❌ ${validationResult.error}`}\n        </div>\n      )}\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-4\">\n        <div className=\"lg:col-span-3\">\n          <div className=\"border border-gray-300 rounded-md overflow-hidden\">\n            <Editor\n              height=\"500px\"\n              defaultLanguage=\"javascript\"\n              value={code}\n              onChange={(value) => setCode(value || '')}\n              theme=\"vs-dark\"\n              options={{\n                minimap: { enabled: false },\n                fontSize: 14,\n                lineNumbers: 'on',\n                roundedSelection: false,\n                scrollBeyondLastLine: false,\n                automaticLayout: true,\n              }}\n            />\n          </div>\n        </div>\n\n        <div className=\"space-y-4\">\n          <div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-3\">Examples</h3>\n            <div className=\"space-y-2\">\n              {examples.map((example, index) => (\n                <button\n                  key={index}\n                  onClick={() => insertExample(example)}\n                  className=\"w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-md border\"\n                >\n                  <div className=\"font-medium text-sm text-gray-900\">{example.name}</div>\n                  <div className=\"text-xs text-gray-600 mt-1\">{example.description}</div>\n                </button>\n              ))}\n            </div>\n          </div>\n\n          <div className=\"bg-blue-50 border border-blue-200 rounded-md p-4\">\n            <h4 className=\"text-sm font-medium text-blue-900 mb-2\">Bot Rot API</h4>\n            <div className=\"text-xs text-blue-800 space-y-1\">\n              <div><code>bot.onMessage(callback)</code></div>\n              <div><code>bot.onReady(callback)</code></div>\n              <div><code>bot.command(name, desc, options)</code></div>\n              <div><code>bot.embed(title, desc, color)</code></div>\n              <div><code>bot.button(label, id, style)</code></div>\n              <div><code>bot.reply(interaction, content)</code></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;;;AAJA;;;;AAoBA,MAAM,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6ClB,CAAC;AAEJ,MAAM,WAAW;IACf;QACE,MAAM;QACN,aAAa;QACb,MAAM,CAAC;;;;;;;;GAQR,CAAC;IACF;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,CAAC;;;;;;;;;;GAUR,CAAC;IACF;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,CAAC;;;;;;;;;;;;;GAaR,CAAC;IACF;CACD;AAEc,SAAS,WAAW,EAAE,GAAG,EAAE,YAAY,EAAmB;;IACvE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6C;IAEpG,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG;QAAC,IAAI,EAAE;KAAC;IAEX,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,IAAI,EAAE,EAAE,EAAE;gBACvE,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,QAAQ,KAAK,IAAI,IAAI;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,UAAU;QACV,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,IAAI,EAAE,EAAE,EAAE;gBACvE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM,UAAU;gBAAa;YACtD;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,iBAAiB;QACrB,cAAc;QACd,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,IAAI,EAAE,CAAC,SAAS,CAAC,EAAE;gBAChF,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAK;YAC9B;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,oBAAoB;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,eAAe;QACnB,aAAa;QACb,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,kBAAkB;YAClB,MAAM,MAAM,CAAC,+BAA+B,EAAE,IAAI,EAAE,EAAE,EAAE;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM,UAAU;gBAAa;YACtD;YAEA,aAAa;YACb,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,IAAI,EAAE,CAAC,OAAO,CAAC,EAAE;gBAC9E,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,aAAa;oBAAE,GAAG,GAAG;oBAAE,QAAQ;gBAAS;gBACxC,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,QAAQ,QAAQ,IAAI;IACtB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;;8CAEV,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCACf,SAAS,cAAc;;;;;;;sCAG1B,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;;8CAEV,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCACnB,aAAa,kBAAkB;;;;;;;sCAGlC,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;;8CAEV,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCACd,YAAY,iBAAiB;;;;;;;;;;;;;;;;;;YAKnC,kCACC,6LAAC;gBAAI,WAAW,CAAC,eAAe,EAC9B,iBAAiB,KAAK,GAClB,wDACA,iDACJ;0BACC,iBAAiB,KAAK,GAAG,2BAA2B,CAAC,EAAE,EAAE,iBAAiB,KAAK,EAAE;;;;;;0BAItF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gLAAA,CAAA,SAAM;gCACL,QAAO;gCACP,iBAAgB;gCAChB,OAAO;gCACP,UAAU,CAAC,QAAU,QAAQ,SAAS;gCACtC,OAAM;gCACN,SAAS;oCACP,SAAS;wCAAE,SAAS;oCAAM;oCAC1B,UAAU;oCACV,aAAa;oCACb,kBAAkB;oCAClB,sBAAsB;oCACtB,iBAAiB;gCACnB;;;;;;;;;;;;;;;;kCAKN,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;gDAEC,SAAS,IAAM,cAAc;gDAC7B,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;kEAAqC,QAAQ,IAAI;;;;;;kEAChE,6LAAC;wDAAI,WAAU;kEAA8B,QAAQ,WAAW;;;;;;;+CAL3D;;;;;;;;;;;;;;;;0CAWb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAI,cAAA,6LAAC;8DAAK;;;;;;;;;;;0DACX,6LAAC;0DAAI,cAAA,6LAAC;8DAAK;;;;;;;;;;;0DACX,6LAAC;0DAAI,cAAA,6LAAC;8DAAK;;;;;;;;;;;0DACX,6LAAC;0DAAI,cAAA,6LAAC;8DAAK;;;;;;;;;;;0DACX,6LAAC;0DAAI,cAAA,6LAAC;8DAAK;;;;;;;;;;;0DACX,6LAAC;0DAAI,cAAA,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzB;GA9NwB;KAAA", "debugId": null}}, {"offset": {"line": 1482, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TFE/build-rot/build-rot-frontend/src/lib/cardDefinitions.ts"], "sourcesContent": ["import {\n  MessageSquare,\n  User,\n  Hash,\n  Play,\n  Globe,\n  Send,\n  Download,\n  Upload,\n  Database,\n  Zap,\n  Filter,\n  GitBranch,\n  Clock,\n  Settings,\n  X\n} from 'lucide-react';\n\nexport interface CardInput {\n  key: string;\n  label: string;\n  type: string;\n  required?: boolean;\n  editable?: boolean;\n  inputType?: string;\n  defaultValue?: string;\n  placeholder?: string;\n}\n\nexport interface CardOutput {\n  key: string;\n  label: string;\n  type: string;\n}\n\nexport interface CardConfig {\n  key: string;\n  label: string;\n  type: 'text' | 'textarea' | 'select' | 'number' | 'checkbox';\n  inputType?: string;\n  defaultValue?: string;\n  placeholder?: string;\n  options?: { label: string; value: string }[];\n}\n\nexport interface CardDefinition {\n  type: string;\n  name: string;\n  description: string;\n  category: string;\n  icon: any;\n  color: string;\n  inputs: CardInput[];\n  outputs: CardOutput[];\n  configurable: boolean;\n  config?: CardConfig[];\n}\n\nexport const cardDefinitions: CardDefinition[] = [\n  // Discord Events\n  {\n    type: 'message_received',\n    name: 'Message Received',\n    description: 'Triggers when a Discord message is received',\n    category: 'Events',\n    icon: MessageSquare,\n    color: 'bg-blue-600',\n    inputs: [],\n    outputs: [\n      { key: 'trigger', label: 'Trigger', type: 'any' },\n      { key: 'message', label: 'Message Object', type: 'message' },\n      { key: 'content', label: 'Message Content', type: 'text' },\n      { key: 'author', label: 'Author Object', type: 'user' },\n      { key: 'author_name', label: 'Author Name', type: 'text' },\n      { key: 'author_id', label: 'Author ID', type: 'text' },\n      { key: 'channel', label: 'Channel Object', type: 'channel' },\n      { key: 'channel_name', label: 'Channel Name', type: 'text' },\n      { key: 'guild_name', label: 'Server Name', type: 'text' }\n    ],\n    configurable: true,\n    config: [\n      {\n        key: 'trigger',\n        label: 'Trigger Type',\n        type: 'select',\n        defaultValue: 'all',\n        options: [\n          { label: 'All Messages', value: 'all' },\n          { label: 'Starts With', value: 'startsWith' },\n          { label: 'Contains', value: 'contains' },\n          { label: 'Exact Match', value: 'exact' }\n        ]\n      },\n      {\n        key: 'triggerValue',\n        label: 'Trigger Value',\n        type: 'text',\n        placeholder: 'e.g., !hello'\n      }\n    ]\n  },\n\n  {\n    type: 'member_join',\n    name: 'Member Joined',\n    description: 'Triggers when a member joins the Discord server',\n    category: 'Events',\n    icon: User,\n    color: 'bg-green-600',\n    inputs: [],\n    outputs: [\n      { key: 'trigger', label: 'Trigger', type: 'any' },\n      { key: 'member', label: 'Member Object', type: 'user' },\n      { key: 'username', label: 'Username', type: 'text' },\n      { key: 'user_id', label: 'User ID', type: 'text' },\n      { key: 'display_name', label: 'Display Name', type: 'text' },\n      { key: 'guild', label: 'Guild Object', type: 'object' },\n      { key: 'guild_name', label: 'Server Name', type: 'text' },\n      { key: 'member_count', label: 'Member Count', type: 'number' }\n    ],\n    configurable: false\n  },\n\n  // HTTP Requests\n  {\n    type: 'http_get',\n    name: 'HTTP GET',\n    description: 'Make a GET request to a URL with variable support',\n    category: 'HTTP',\n    icon: Download,\n    color: 'bg-cyan-600',\n    inputs: [\n      { key: 'trigger', label: 'Trigger', type: 'any' }\n    ],\n    outputs: [\n      { key: 'response', label: 'Response', type: 'object' },\n      { key: 'data', label: 'Data', type: 'any' },\n      { key: 'status', label: 'Status', type: 'number' },\n      { key: 'error', label: 'Error', type: 'text' }\n    ],\n    configurable: true,\n    config: [\n      {\n        key: 'url',\n        label: 'URL',\n        type: 'text',\n        placeholder: 'https://localhost:5001/api/bots/{{BOT_ID}}'\n      },\n      {\n        key: 'headers',\n        label: 'Headers (JSON)',\n        type: 'textarea',\n        placeholder: '{\"Authorization\": \"Bearer {{TOKEN}}\", \"Content-Type\": \"application/json\"}'\n      }\n    ]\n  },\n\n  {\n    type: 'http_post',\n    name: 'HTTP POST',\n    description: 'Make a POST request to a URL with variable support',\n    category: 'HTTP',\n    icon: Upload,\n    color: 'bg-indigo-600',\n    inputs: [\n      { key: 'trigger', label: 'Trigger', type: 'any' },\n      { key: 'data', label: 'Data', type: 'object' }\n    ],\n    outputs: [\n      { key: 'response', label: 'Response', type: 'object' },\n      { key: 'data', label: 'Data', type: 'any' },\n      { key: 'status', label: 'Status', type: 'number' },\n      { key: 'error', label: 'Error', type: 'text' }\n    ],\n    configurable: true,\n    config: [\n      {\n        key: 'url',\n        label: 'URL',\n        type: 'text',\n        placeholder: 'https://localhost:5001/api/bots/{{BOT_ID}}/start'\n      },\n      {\n        key: 'body',\n        label: 'Request Body (JSON)',\n        type: 'textarea',\n        placeholder: '{\"message\": \"{{content}}\", \"userId\": \"{{author.id}}\"}'\n      },\n      {\n        key: 'headers',\n        label: 'Headers (JSON)',\n        type: 'textarea',\n        placeholder: '{\"Content-Type\": \"application/json\", \"Authorization\": \"Bearer {{TOKEN}}\"}'\n      }\n    ]\n  },\n\n  {\n    type: 'http_put',\n    name: 'HTTP PUT',\n    description: 'Make a PUT request to a URL with variable support',\n    category: 'HTTP',\n    icon: Settings,\n    color: 'bg-violet-600',\n    inputs: [\n      { key: 'trigger', label: 'Trigger', type: 'any' },\n      { key: 'data', label: 'Data', type: 'object' }\n    ],\n    outputs: [\n      { key: 'response', label: 'Response', type: 'object' },\n      { key: 'data', label: 'Data', type: 'any' },\n      { key: 'status', label: 'Status', type: 'number' },\n      { key: 'error', label: 'Error', type: 'text' }\n    ],\n    configurable: true,\n    config: [\n      {\n        key: 'url',\n        label: 'URL',\n        type: 'text',\n        placeholder: 'https://localhost:5001/api/bots/{{BOT_ID}}'\n      },\n      {\n        key: 'body',\n        label: 'Request Body (JSON)',\n        type: 'textarea',\n        placeholder: '{\"name\": \"{{BOT_NAME}}\", \"description\": \"Updated bot\"}'\n      },\n      {\n        key: 'headers',\n        label: 'Headers (JSON)',\n        type: 'textarea',\n        placeholder: '{\"Content-Type\": \"application/json\", \"Authorization\": \"Bearer {{TOKEN}}\"}'\n      }\n    ]\n  },\n\n  {\n    type: 'http_delete',\n    name: 'HTTP DELETE',\n    description: 'Make a DELETE request to a URL with variable support',\n    category: 'HTTP',\n    icon: X,\n    color: 'bg-red-600',\n    inputs: [\n      { key: 'trigger', label: 'Trigger', type: 'any' }\n    ],\n    outputs: [\n      { key: 'response', label: 'Response', type: 'object' },\n      { key: 'status', label: 'Status', type: 'number' },\n      { key: 'success', label: 'Success', type: 'boolean' },\n      { key: 'error', label: 'Error', type: 'text' }\n    ],\n    configurable: true,\n    config: [\n      {\n        key: 'url',\n        label: 'URL',\n        type: 'text',\n        placeholder: 'https://localhost:5001/api/bots/{{BOT_ID}}'\n      },\n      {\n        key: 'headers',\n        label: 'Headers (JSON)',\n        type: 'textarea',\n        placeholder: '{\"Authorization\": \"Bearer {{TOKEN}}\"}'\n      }\n    ]\n  },\n\n  // Discord Actions\n  {\n    type: 'send_message',\n    name: 'Send Message',\n    description: 'Send a message to a Discord channel',\n    category: 'Actions',\n    icon: Send,\n    color: 'bg-purple-600',\n    inputs: [\n      { key: 'trigger', label: 'Trigger', type: 'any' },\n      { key: 'channel', label: 'Channel', type: 'channel' },\n      { key: 'content', label: 'Message Text', type: 'text' },\n      { key: 'username', label: 'Username (optional)', type: 'text' },\n      { key: 'user_id', label: 'User ID (optional)', type: 'text' }\n    ],\n    outputs: [\n      { key: 'message', label: 'Sent Message', type: 'message' },\n      { key: 'success', label: 'Success', type: 'boolean' },\n      { key: 'continue', label: 'Continue', type: 'any' }\n    ],\n    configurable: true,\n    config: [\n      {\n        key: 'content',\n        label: 'Message Content',\n        type: 'textarea',\n        placeholder: 'Welcome {{username}} to {{guild_name}}! You are member #{{member_count}}.'\n      },\n      {\n        key: 'channelName',\n        label: 'Channel Name (if no channel input)',\n        type: 'text',\n        placeholder: 'general'\n      }\n    ]\n  },\n\n  {\n    type: 'reply_message',\n    name: 'Reply to Message',\n    description: 'Reply to the original Discord message',\n    category: 'Actions',\n    icon: MessageSquare,\n    color: 'bg-pink-600',\n    inputs: [\n      { key: 'trigger', label: 'Trigger', type: 'any' },\n      { key: 'message', label: 'Original Message', type: 'message' },\n      { key: 'content', label: 'Reply Text', type: 'text' },\n      { key: 'author_name', label: 'Author Name (optional)', type: 'text' }\n    ],\n    outputs: [\n      { key: 'reply', label: 'Reply Message', type: 'message' },\n      { key: 'success', label: 'Success', type: 'boolean' },\n      { key: 'continue', label: 'Continue', type: 'any' }\n    ],\n    configurable: true,\n    config: [\n      {\n        key: 'content',\n        label: 'Reply Content',\n        type: 'textarea',\n        placeholder: 'Thanks for your message, {{author_name}}!'\n      }\n    ]\n  },\n\n  {\n    type: 'get_channel',\n    name: 'Get Channel',\n    description: 'Get a channel by name or ID',\n    category: 'Actions',\n    icon: Hash,\n    color: 'bg-teal-600',\n    inputs: [\n      { key: 'trigger', label: 'Trigger', type: 'any' },\n      { key: 'channel_name', label: 'Channel Name', type: 'text' }\n    ],\n    outputs: [\n      { key: 'channel', label: 'Channel Object', type: 'channel' },\n      { key: 'channel_name', label: 'Channel Name', type: 'text' },\n      { key: 'channel_id', label: 'Channel ID', type: 'text' },\n      { key: 'found', label: 'Found', type: 'boolean' }\n    ],\n    configurable: true,\n    config: [\n      {\n        key: 'channelName',\n        label: 'Channel Name',\n        type: 'text',\n        placeholder: 'general'\n      }\n    ]\n  },\n\n  {\n    type: 'text_format',\n    name: 'Format Text',\n    description: 'Format text with variables and templates',\n    category: 'Actions',\n    icon: Settings,\n    color: 'bg-amber-600',\n    inputs: [\n      { key: 'trigger', label: 'Trigger', type: 'any' },\n      { key: 'username', label: 'Username', type: 'text' },\n      { key: 'user_id', label: 'User ID', type: 'text' },\n      { key: 'guild_name', label: 'Server Name', type: 'text' },\n      { key: 'member_count', label: 'Member Count', type: 'number' },\n      { key: 'custom_value', label: 'Custom Value', type: 'text' }\n    ],\n    outputs: [\n      { key: 'formatted_text', label: 'Formatted Text', type: 'text' },\n      { key: 'continue', label: 'Continue', type: 'any' }\n    ],\n    configurable: true,\n    config: [\n      {\n        key: 'template',\n        label: 'Text Template',\n        type: 'textarea',\n        placeholder: 'Welcome {{username}} to {{guild_name}}! You are member #{{member_count}}. {{custom_value}}'\n      }\n    ]\n  },\n\n  // Logic & Control\n  {\n    type: 'condition',\n    name: 'Condition',\n    description: 'Check a condition and branch execution',\n    category: 'Logic',\n    icon: GitBranch,\n    color: 'bg-orange-600',\n    inputs: [\n      { key: 'trigger', label: 'Trigger', type: 'any' },\n      { key: 'value1', label: 'Value 1', type: 'any', editable: true },\n      { key: 'value2', label: 'Value 2', type: 'any', editable: true }\n    ],\n    outputs: [\n      { key: 'true', label: 'True', type: 'any' },\n      { key: 'false', label: 'False', type: 'any' }\n    ],\n    configurable: true,\n    config: [\n      {\n        key: 'operator',\n        label: 'Operator',\n        type: 'select',\n        defaultValue: 'equals',\n        options: [\n          { label: 'Equals', value: 'equals' },\n          { label: 'Not Equals', value: 'notEquals' },\n          { label: 'Contains', value: 'contains' },\n          { label: 'Starts With', value: 'startsWith' },\n          { label: 'Greater Than', value: 'greaterThan' },\n          { label: 'Less Than', value: 'lessThan' }\n        ]\n      }\n    ]\n  },\n\n  {\n    type: 'filter',\n    name: 'Filter',\n    description: 'Filter data based on conditions',\n    category: 'Logic',\n    icon: Filter,\n    color: 'bg-yellow-600',\n    inputs: [\n      { key: 'data', label: 'Data', type: 'any' },\n      { key: 'condition', label: 'Condition', type: 'text', editable: true }\n    ],\n    outputs: [\n      { key: 'filtered', label: 'Filtered Data', type: 'any' },\n      { key: 'count', label: 'Count', type: 'number' }\n    ],\n    configurable: true,\n    config: [\n      {\n        key: 'property',\n        label: 'Property to Filter',\n        type: 'text',\n        placeholder: 'username'\n      },\n      {\n        key: 'value',\n        label: 'Filter Value',\n        type: 'text',\n        placeholder: 'admin'\n      }\n    ]\n  },\n\n  // Utilities\n  {\n    type: 'delay',\n    name: 'Delay',\n    description: 'Add a delay before continuing',\n    category: 'Utilities',\n    icon: Clock,\n    color: 'bg-gray-600',\n    inputs: [\n      { key: 'trigger', label: 'Trigger', type: 'any' }\n    ],\n    outputs: [\n      { key: 'continue', label: 'Continue', type: 'any' }\n    ],\n    configurable: true,\n    config: [\n      {\n        key: 'duration',\n        label: 'Duration (seconds)',\n        type: 'number',\n        defaultValue: '1'\n      }\n    ]\n  },\n\n  {\n    type: 'variable_set',\n    name: 'Set Variable',\n    description: 'Set a variable value',\n    category: 'Utilities',\n    icon: Database,\n    color: 'bg-teal-600',\n    inputs: [\n      { key: 'trigger', label: 'Trigger', type: 'any' },\n      { key: 'value', label: 'Value', type: 'any', editable: true }\n    ],\n    outputs: [\n      { key: 'variable', label: 'Variable', type: 'any' },\n      { key: 'continue', label: 'Continue', type: 'any' }\n    ],\n    configurable: true,\n    config: [\n      {\n        key: 'variableName',\n        label: 'Variable Name',\n        type: 'text',\n        placeholder: 'myVariable'\n      },\n      {\n        key: 'value',\n        label: 'Value',\n        type: 'text',\n        placeholder: 'Hello World'\n      }\n    ]\n  }\n];\n\nexport function getCardDefinition(type: string): CardDefinition {\n  return cardDefinitions.find(def => def.type === type) || cardDefinitions[0];\n}\n\nexport function getCardsByCategory(): Record<string, CardDefinition[]> {\n  return cardDefinitions.reduce((acc, card) => {\n    if (!acc[card.category]) {\n      acc[card.category] = [];\n    }\n    acc[card.category].push(card);\n    return acc;\n  }, {} as Record<string, CardDefinition[]>);\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AA0DO,MAAM,kBAAoC;IAC/C,iBAAiB;IACjB;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM,2NAAA,CAAA,gBAAa;QACnB,OAAO;QACP,QAAQ,EAAE;QACV,SAAS;YACP;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM;YAAM;YAChD;gBAAE,KAAK;gBAAW,OAAO;gBAAkB,MAAM;YAAU;YAC3D;gBAAE,KAAK;gBAAW,OAAO;gBAAmB,MAAM;YAAO;YACzD;gBAAE,KAAK;gBAAU,OAAO;gBAAiB,MAAM;YAAO;YACtD;gBAAE,KAAK;gBAAe,OAAO;gBAAe,MAAM;YAAO;YACzD;gBAAE,KAAK;gBAAa,OAAO;gBAAa,MAAM;YAAO;YACrD;gBAAE,KAAK;gBAAW,OAAO;gBAAkB,MAAM;YAAU;YAC3D;gBAAE,KAAK;gBAAgB,OAAO;gBAAgB,MAAM;YAAO;YAC3D;gBAAE,KAAK;gBAAc,OAAO;gBAAe,MAAM;YAAO;SACzD;QACD,cAAc;QACd,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,cAAc;gBACd,SAAS;oBACP;wBAAE,OAAO;wBAAgB,OAAO;oBAAM;oBACtC;wBAAE,OAAO;wBAAe,OAAO;oBAAa;oBAC5C;wBAAE,OAAO;wBAAY,OAAO;oBAAW;oBACvC;wBAAE,OAAO;wBAAe,OAAO;oBAAQ;iBACxC;YACH;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;SACD;IACH;IAEA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;QACP,QAAQ,EAAE;QACV,SAAS;YACP;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM;YAAM;YAChD;gBAAE,KAAK;gBAAU,OAAO;gBAAiB,MAAM;YAAO;YACtD;gBAAE,KAAK;gBAAY,OAAO;gBAAY,MAAM;YAAO;YACnD;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM;YAAO;YACjD;gBAAE,KAAK;gBAAgB,OAAO;gBAAgB,MAAM;YAAO;YAC3D;gBAAE,KAAK;gBAAS,OAAO;gBAAgB,MAAM;YAAS;YACtD;gBAAE,KAAK;gBAAc,OAAO;gBAAe,MAAM;YAAO;YACxD;gBAAE,KAAK;gBAAgB,OAAO;gBAAgB,MAAM;YAAS;SAC9D;QACD,cAAc;IAChB;IAEA,gBAAgB;IAChB;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM,6MAAA,CAAA,WAAQ;QACd,OAAO;QACP,QAAQ;YACN;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM;YAAM;SACjD;QACD,SAAS;YACP;gBAAE,KAAK;gBAAY,OAAO;gBAAY,MAAM;YAAS;YACrD;gBAAE,KAAK;gBAAQ,OAAO;gBAAQ,MAAM;YAAM;YAC1C;gBAAE,KAAK;gBAAU,OAAO;gBAAU,MAAM;YAAS;YACjD;gBAAE,KAAK;gBAAS,OAAO;gBAAS,MAAM;YAAO;SAC9C;QACD,cAAc;QACd,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;SACD;IACH;IAEA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;QACP,QAAQ;YACN;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM;YAAM;YAChD;gBAAE,KAAK;gBAAQ,OAAO;gBAAQ,MAAM;YAAS;SAC9C;QACD,SAAS;YACP;gBAAE,KAAK;gBAAY,OAAO;gBAAY,MAAM;YAAS;YACrD;gBAAE,KAAK;gBAAQ,OAAO;gBAAQ,MAAM;YAAM;YAC1C;gBAAE,KAAK;gBAAU,OAAO;gBAAU,MAAM;YAAS;YACjD;gBAAE,KAAK;gBAAS,OAAO;gBAAS,MAAM;YAAO;SAC9C;QACD,cAAc;QACd,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;SACD;IACH;IAEA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM,6MAAA,CAAA,WAAQ;QACd,OAAO;QACP,QAAQ;YACN;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM;YAAM;YAChD;gBAAE,KAAK;gBAAQ,OAAO;gBAAQ,MAAM;YAAS;SAC9C;QACD,SAAS;YACP;gBAAE,KAAK;gBAAY,OAAO;gBAAY,MAAM;YAAS;YACrD;gBAAE,KAAK;gBAAQ,OAAO;gBAAQ,MAAM;YAAM;YAC1C;gBAAE,KAAK;gBAAU,OAAO;gBAAU,MAAM;YAAS;YACjD;gBAAE,KAAK;gBAAS,OAAO;gBAAS,MAAM;YAAO;SAC9C;QACD,cAAc;QACd,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;SACD;IACH;IAEA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM,+LAAA,CAAA,IAAC;QACP,OAAO;QACP,QAAQ;YACN;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM;YAAM;SACjD;QACD,SAAS;YACP;gBAAE,KAAK;gBAAY,OAAO;gBAAY,MAAM;YAAS;YACrD;gBAAE,KAAK;gBAAU,OAAO;gBAAU,MAAM;YAAS;YACjD;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM;YAAU;YACpD;gBAAE,KAAK;gBAAS,OAAO;gBAAS,MAAM;YAAO;SAC9C;QACD,cAAc;QACd,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;SACD;IACH;IAEA,kBAAkB;IAClB;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;QACP,QAAQ;YACN;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM;YAAM;YAChD;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM;YAAU;YACpD;gBAAE,KAAK;gBAAW,OAAO;gBAAgB,MAAM;YAAO;YACtD;gBAAE,KAAK;gBAAY,OAAO;gBAAuB,MAAM;YAAO;YAC9D;gBAAE,KAAK;gBAAW,OAAO;gBAAsB,MAAM;YAAO;SAC7D;QACD,SAAS;YACP;gBAAE,KAAK;gBAAW,OAAO;gBAAgB,MAAM;YAAU;YACzD;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM;YAAU;YACpD;gBAAE,KAAK;gBAAY,OAAO;gBAAY,MAAM;YAAM;SACnD;QACD,cAAc;QACd,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;SACD;IACH;IAEA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM,2NAAA,CAAA,gBAAa;QACnB,OAAO;QACP,QAAQ;YACN;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM;YAAM;YAChD;gBAAE,KAAK;gBAAW,OAAO;gBAAoB,MAAM;YAAU;YAC7D;gBAAE,KAAK;gBAAW,OAAO;gBAAc,MAAM;YAAO;YACpD;gBAAE,KAAK;gBAAe,OAAO;gBAA0B,MAAM;YAAO;SACrE;QACD,SAAS;YACP;gBAAE,KAAK;gBAAS,OAAO;gBAAiB,MAAM;YAAU;YACxD;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM;YAAU;YACpD;gBAAE,KAAK;gBAAY,OAAO;gBAAY,MAAM;YAAM;SACnD;QACD,cAAc;QACd,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;SACD;IACH;IAEA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;QACP,QAAQ;YACN;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM;YAAM;YAChD;gBAAE,KAAK;gBAAgB,OAAO;gBAAgB,MAAM;YAAO;SAC5D;QACD,SAAS;YACP;gBAAE,KAAK;gBAAW,OAAO;gBAAkB,MAAM;YAAU;YAC3D;gBAAE,KAAK;gBAAgB,OAAO;gBAAgB,MAAM;YAAO;YAC3D;gBAAE,KAAK;gBAAc,OAAO;gBAAc,MAAM;YAAO;YACvD;gBAAE,KAAK;gBAAS,OAAO;gBAAS,MAAM;YAAU;SACjD;QACD,cAAc;QACd,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;SACD;IACH;IAEA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM,6MAAA,CAAA,WAAQ;QACd,OAAO;QACP,QAAQ;YACN;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM;YAAM;YAChD;gBAAE,KAAK;gBAAY,OAAO;gBAAY,MAAM;YAAO;YACnD;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM;YAAO;YACjD;gBAAE,KAAK;gBAAc,OAAO;gBAAe,MAAM;YAAO;YACxD;gBAAE,KAAK;gBAAgB,OAAO;gBAAgB,MAAM;YAAS;YAC7D;gBAAE,KAAK;gBAAgB,OAAO;gBAAgB,MAAM;YAAO;SAC5D;QACD,SAAS;YACP;gBAAE,KAAK;gBAAkB,OAAO;gBAAkB,MAAM;YAAO;YAC/D;gBAAE,KAAK;gBAAY,OAAO;gBAAY,MAAM;YAAM;SACnD;QACD,cAAc;QACd,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;SACD;IACH;IAEA,kBAAkB;IAClB;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM,mNAAA,CAAA,YAAS;QACf,OAAO;QACP,QAAQ;YACN;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM;YAAM;YAChD;gBAAE,KAAK;gBAAU,OAAO;gBAAW,MAAM;gBAAO,UAAU;YAAK;YAC/D;gBAAE,KAAK;gBAAU,OAAO;gBAAW,MAAM;gBAAO,UAAU;YAAK;SAChE;QACD,SAAS;YACP;gBAAE,KAAK;gBAAQ,OAAO;gBAAQ,MAAM;YAAM;YAC1C;gBAAE,KAAK;gBAAS,OAAO;gBAAS,MAAM;YAAM;SAC7C;QACD,cAAc;QACd,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,cAAc;gBACd,SAAS;oBACP;wBAAE,OAAO;wBAAU,OAAO;oBAAS;oBACnC;wBAAE,OAAO;wBAAc,OAAO;oBAAY;oBAC1C;wBAAE,OAAO;wBAAY,OAAO;oBAAW;oBACvC;wBAAE,OAAO;wBAAe,OAAO;oBAAa;oBAC5C;wBAAE,OAAO;wBAAgB,OAAO;oBAAc;oBAC9C;wBAAE,OAAO;wBAAa,OAAO;oBAAW;iBACzC;YACH;SACD;IACH;IAEA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;QACP,QAAQ;YACN;gBAAE,KAAK;gBAAQ,OAAO;gBAAQ,MAAM;YAAM;YAC1C;gBAAE,KAAK;gBAAa,OAAO;gBAAa,MAAM;gBAAQ,UAAU;YAAK;SACtE;QACD,SAAS;YACP;gBAAE,KAAK;gBAAY,OAAO;gBAAiB,MAAM;YAAM;YACvD;gBAAE,KAAK;gBAAS,OAAO;gBAAS,MAAM;YAAS;SAChD;QACD,cAAc;QACd,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;SACD;IACH;IAEA,YAAY;IACZ;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,QAAQ;YACN;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM;YAAM;SACjD;QACD,SAAS;YACP;gBAAE,KAAK;gBAAY,OAAO;gBAAY,MAAM;YAAM;SACnD;QACD,cAAc;QACd,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,cAAc;YAChB;SACD;IACH;IAEA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM,6MAAA,CAAA,WAAQ;QACd,OAAO;QACP,QAAQ;YACN;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM;YAAM;YAChD;gBAAE,KAAK;gBAAS,OAAO;gBAAS,MAAM;gBAAO,UAAU;YAAK;SAC7D;QACD,SAAS;YACP;gBAAE,KAAK;gBAAY,OAAO;gBAAY,MAAM;YAAM;YAClD;gBAAE,KAAK;gBAAY,OAAO;gBAAY,MAAM;YAAM;SACnD;QACD,cAAc;QACd,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;SACD;IACH;CACD;AAEM,SAAS,kBAAkB,IAAY;IAC5C,OAAO,gBAAgB,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,SAAS,eAAe,CAAC,EAAE;AAC7E;AAEO,SAAS;IACd,OAAO,gBAAgB,MAAM,CAAC,CAAC,KAAK;QAClC,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,EAAE;YACvB,GAAG,CAAC,KAAK,QAAQ,CAAC,GAAG,EAAE;QACzB;QACA,GAAG,CAAC,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC;QACxB,OAAO;IACT,GAAG,CAAC;AACN", "debugId": null}}, {"offset": {"line": 2335, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TFE/build-rot/build-rot-frontend/src/components/BuilderCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { X, Circle, Square } from 'lucide-react';\nimport { getCardDefinition } from '../lib/cardDefinitions';\n\ninterface CardInstance {\n  id: string;\n  type: string;\n  position: { x: number; y: number };\n  data: Record<string, any>;\n}\n\ninterface BuilderCardProps {\n  card: CardInstance;\n  isSelected: boolean;\n  onMouseDown: (e: React.MouseEvent) => void;\n  onUpdate: (data: Record<string, any>) => void;\n  onDelete: () => void;\n  onStartConnection: (cardId: string, output: string, outputType: string) => void;\n  onCompleteConnection: (cardId: string, input: string, inputType: string) => void;\n  connecting: { cardId: string; output: string; outputType: string } | null;\n  variables: Record<string, string>;\n}\n\nexport default function BuilderCard({\n  card,\n  isSelected,\n  onMouseDown,\n  onUpdate,\n  onDelete,\n  onStartConnection,\n  onCompleteConnection,\n  connecting,\n  variables\n}: BuilderCardProps) {\n  const [isEditing, setIsEditing] = useState(false);\n  const cardDef = getCardDefinition(card.type);\n\n  const handleInputChange = (inputKey: string, value: string) => {\n    onUpdate({ [inputKey]: value });\n  };\n\n  const handleOutputClick = (output: any) => {\n    if (connecting) return;\n    onStartConnection(card.id, output.key, output.type);\n  };\n\n  const handleInputClick = (input: any) => {\n    if (!connecting) return;\n    onCompleteConnection(card.id, input.key, input.type);\n  };\n\n  const replaceVariables = (text: string) => {\n    let result = text;\n    Object.entries(variables).forEach(([key, value]) => {\n      result = result.replace(new RegExp(`{{${key}}}`, 'g'), value);\n    });\n    return result;\n  };\n\n  const getTypeColor = (type: string) => {\n    const colors = {\n      text: 'bg-blue-500',\n      number: 'bg-green-500',\n      boolean: 'bg-purple-500',\n      object: 'bg-orange-500',\n      array: 'bg-red-500',\n      any: 'bg-gray-500',\n      message: 'bg-indigo-500',\n      user: 'bg-pink-500',\n      channel: 'bg-teal-500',\n      embed: 'bg-yellow-500',\n      http: 'bg-cyan-500'\n    };\n    return colors[type as keyof typeof colors] || 'bg-gray-500';\n  };\n\n  return (\n    <div\n      className={`absolute bg-white rounded-lg shadow-lg border-2 min-w-48 ${\n        isSelected ? 'border-blue-500' : 'border-gray-200'\n      } ${connecting?.cardId === card.id ? 'ring-2 ring-blue-300' : ''}`}\n      style={{\n        left: card.position.x,\n        top: card.position.y,\n        zIndex: isSelected ? 10 : 1\n      }}\n      onMouseDown={onMouseDown}\n    >\n      {/* Header */}\n      <div className={`px-3 py-2 rounded-t-lg ${cardDef.color} text-white flex items-center justify-between`}>\n        <div className=\"flex items-center\">\n          <cardDef.icon className=\"h-4 w-4 mr-2\" />\n          <span className=\"font-medium text-sm\">{cardDef.name}</span>\n        </div>\n        <button\n          onClick={(e) => {\n            e.stopPropagation();\n            onDelete();\n          }}\n          className=\"text-white hover:text-red-200\"\n        >\n          <X className=\"h-4 w-4\" />\n        </button>\n      </div>\n\n      {/* Inputs */}\n      {cardDef.inputs.length > 0 && (\n        <div className=\"px-3 py-2 border-b\">\n          <div className=\"text-xs font-medium text-gray-600 mb-2\">Inputs</div>\n          {cardDef.inputs.map((input) => (\n            <div key={input.key} className=\"flex items-center mb-2 last:mb-0\">\n              <button\n                className={`w-3 h-3 rounded-full mr-2 border-2 border-white ${getTypeColor(input.type)} ${\n                  connecting && connecting.outputType === input.type ? 'ring-2 ring-blue-300' : ''\n                }`}\n                onClick={() => handleInputClick(input)}\n                title={`${input.label} (${input.type})`}\n              >\n                <Circle className=\"w-full h-full\" />\n              </button>\n              <div className=\"flex-1\">\n                <div className=\"text-xs text-gray-700\">{input.label}</div>\n                {input.editable && (\n                  <input\n                    type={input.inputType || 'text'}\n                    value={card.data[input.key] || input.defaultValue || ''}\n                    onChange={(e) => handleInputChange(input.key, e.target.value)}\n                    placeholder={input.placeholder}\n                    className=\"w-full text-xs border rounded px-1 py-0.5 mt-1\"\n                    onClick={(e) => e.stopPropagation()}\n                  />\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* Content/Configuration */}\n      {cardDef.configurable && (\n        <div className=\"px-3 py-2 border-b\">\n          <div className=\"text-xs font-medium text-gray-600 mb-2\">Configuration</div>\n          {cardDef.config?.map((configItem) => (\n            <div key={configItem.key} className=\"mb-2 last:mb-0\">\n              <label className=\"text-xs text-gray-700 block mb-1\">{configItem.label}</label>\n              {configItem.type === 'select' ? (\n                <select\n                  value={card.data[configItem.key] || configItem.defaultValue || ''}\n                  onChange={(e) => handleInputChange(configItem.key, e.target.value)}\n                  className=\"w-full text-xs border rounded px-1 py-0.5\"\n                  onClick={(e) => e.stopPropagation()}\n                >\n                  {configItem.options?.map((option) => (\n                    <option key={option.value} value={option.value}>\n                      {option.label}\n                    </option>\n                  ))}\n                </select>\n              ) : configItem.type === 'textarea' ? (\n                <textarea\n                  value={card.data[configItem.key] || configItem.defaultValue || ''}\n                  onChange={(e) => handleInputChange(configItem.key, e.target.value)}\n                  placeholder={configItem.placeholder}\n                  className=\"w-full text-xs border rounded px-1 py-0.5 resize-none\"\n                  rows={3}\n                  onClick={(e) => e.stopPropagation()}\n                />\n              ) : (\n                <input\n                  type={configItem.inputType || 'text'}\n                  value={card.data[configItem.key] || configItem.defaultValue || ''}\n                  onChange={(e) => handleInputChange(configItem.key, e.target.value)}\n                  placeholder={configItem.placeholder}\n                  className=\"w-full text-xs border rounded px-1 py-0.5\"\n                  onClick={(e) => e.stopPropagation()}\n                />\n              )}\n              {configItem.key === 'url' && card.data[configItem.key] && (\n                <div className=\"text-xs text-gray-500 mt-1\">\n                  Preview: {replaceVariables(card.data[configItem.key])}\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* Outputs */}\n      {cardDef.outputs.length > 0 && (\n        <div className=\"px-3 py-2\">\n          <div className=\"text-xs font-medium text-gray-600 mb-2\">Outputs</div>\n          {cardDef.outputs.map((output) => (\n            <div key={output.key} className=\"flex items-center justify-end mb-2 last:mb-0\">\n              <div className=\"flex-1 text-right\">\n                <div className=\"text-xs text-gray-700\">{output.label}</div>\n              </div>\n              <button\n                className={`w-3 h-3 rounded-full ml-2 border-2 border-white ${getTypeColor(output.type)}`}\n                onClick={() => handleOutputClick(output)}\n                title={`${output.label} (${output.type})`}\n              >\n                <Square className=\"w-full h-full\" />\n              </button>\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* Status indicator */}\n      {card.data.status && (\n        <div className=\"px-3 py-1 bg-gray-50 rounded-b-lg\">\n          <div className=\"text-xs text-gray-600\">\n            Status: <span className=\"font-medium\">{card.data.status}</span>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAyBe,SAAS,YAAY,EAClC,IAAI,EACJ,UAAU,EACV,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,iBAAiB,EACjB,oBAAoB,EACpB,UAAU,EACV,SAAS,EACQ;;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,IAAI;IAE3C,MAAM,oBAAoB,CAAC,UAAkB;QAC3C,SAAS;YAAE,CAAC,SAAS,EAAE;QAAM;IAC/B;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,YAAY;QAChB,kBAAkB,KAAK,EAAE,EAAE,OAAO,GAAG,EAAE,OAAO,IAAI;IACpD;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,YAAY;QACjB,qBAAqB,KAAK,EAAE,EAAE,MAAM,GAAG,EAAE,MAAM,IAAI;IACrD;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,SAAS;QACb,OAAO,OAAO,CAAC,WAAW,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC7C,SAAS,OAAO,OAAO,CAAC,IAAI,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM;QACzD;QACA,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAS;YACb,MAAM;YACN,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO;YACP,KAAK;YACL,SAAS;YACT,MAAM;YACN,SAAS;YACT,OAAO;YACP,MAAM;QACR;QACA,OAAO,MAAM,CAAC,KAA4B,IAAI;IAChD;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,yDAAyD,EACnE,aAAa,oBAAoB,kBAClC,CAAC,EAAE,YAAY,WAAW,KAAK,EAAE,GAAG,yBAAyB,IAAI;QAClE,OAAO;YACL,MAAM,KAAK,QAAQ,CAAC,CAAC;YACrB,KAAK,KAAK,QAAQ,CAAC,CAAC;YACpB,QAAQ,aAAa,KAAK;QAC5B;QACA,aAAa;;0BAGb,6LAAC;gBAAI,WAAW,CAAC,uBAAuB,EAAE,QAAQ,KAAK,CAAC,6CAA6C,CAAC;;kCACpG,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,QAAQ,IAAI;gCAAC,WAAU;;;;;;0CACxB,6LAAC;gCAAK,WAAU;0CAAuB,QAAQ,IAAI;;;;;;;;;;;;kCAErD,6LAAC;wBACC,SAAS,CAAC;4BACR,EAAE,eAAe;4BACjB;wBACF;wBACA,WAAU;kCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAKhB,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAyC;;;;;;oBACvD,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,sBACnB,6LAAC;4BAAoB,WAAU;;8CAC7B,6LAAC;oCACC,WAAW,CAAC,gDAAgD,EAAE,aAAa,MAAM,IAAI,EAAE,CAAC,EACtF,cAAc,WAAW,UAAU,KAAK,MAAM,IAAI,GAAG,yBAAyB,IAC9E;oCACF,SAAS,IAAM,iBAAiB;oCAChC,OAAO,GAAG,MAAM,KAAK,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;8CAEvC,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAyB,MAAM,KAAK;;;;;;wCAClD,MAAM,QAAQ,kBACb,6LAAC;4CACC,MAAM,MAAM,SAAS,IAAI;4CACzB,OAAO,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,YAAY,IAAI;4CACrD,UAAU,CAAC,IAAM,kBAAkB,MAAM,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK;4CAC5D,aAAa,MAAM,WAAW;4CAC9B,WAAU;4CACV,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;;;;;;;;;2BAnB/B,MAAM,GAAG;;;;;;;;;;;YA6BxB,QAAQ,YAAY,kBACnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAyC;;;;;;oBACvD,QAAQ,MAAM,EAAE,IAAI,CAAC,2BACpB,6LAAC;4BAAyB,WAAU;;8CAClC,6LAAC;oCAAM,WAAU;8CAAoC,WAAW,KAAK;;;;;;gCACpE,WAAW,IAAI,KAAK,yBACnB,6LAAC;oCACC,OAAO,KAAK,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,WAAW,YAAY,IAAI;oCAC/D,UAAU,CAAC,IAAM,kBAAkB,WAAW,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK;oCACjE,WAAU;oCACV,SAAS,CAAC,IAAM,EAAE,eAAe;8CAEhC,WAAW,OAAO,EAAE,IAAI,CAAC,uBACxB,6LAAC;4CAA0B,OAAO,OAAO,KAAK;sDAC3C,OAAO,KAAK;2CADF,OAAO,KAAK;;;;;;;;;2CAK3B,WAAW,IAAI,KAAK,2BACtB,6LAAC;oCACC,OAAO,KAAK,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,WAAW,YAAY,IAAI;oCAC/D,UAAU,CAAC,IAAM,kBAAkB,WAAW,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK;oCACjE,aAAa,WAAW,WAAW;oCACnC,WAAU;oCACV,MAAM;oCACN,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;yDAGnC,6LAAC;oCACC,MAAM,WAAW,SAAS,IAAI;oCAC9B,OAAO,KAAK,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,WAAW,YAAY,IAAI;oCAC/D,UAAU,CAAC,IAAM,kBAAkB,WAAW,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK;oCACjE,aAAa,WAAW,WAAW;oCACnC,WAAU;oCACV,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;;gCAGpC,WAAW,GAAG,KAAK,SAAS,KAAK,IAAI,CAAC,WAAW,GAAG,CAAC,kBACpD,6LAAC;oCAAI,WAAU;;wCAA6B;wCAChC,iBAAiB,KAAK,IAAI,CAAC,WAAW,GAAG,CAAC;;;;;;;;2BApChD,WAAW,GAAG;;;;;;;;;;;YA6C7B,QAAQ,OAAO,CAAC,MAAM,GAAG,mBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAyC;;;;;;oBACvD,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,uBACpB,6LAAC;4BAAqB,WAAU;;8CAC9B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDAAyB,OAAO,KAAK;;;;;;;;;;;8CAEtD,6LAAC;oCACC,WAAW,CAAC,gDAAgD,EAAE,aAAa,OAAO,IAAI,GAAG;oCACzF,SAAS,IAAM,kBAAkB;oCACjC,OAAO,GAAG,OAAO,KAAK,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC;8CAEzC,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;2BATZ,OAAO,GAAG;;;;;;;;;;;YAiBzB,KAAK,IAAI,CAAC,MAAM,kBACf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBAAwB;sCAC7B,6LAAC;4BAAK,WAAU;sCAAe,KAAK,IAAI,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;AAMnE;GAnMwB;KAAA", "debugId": null}}, {"offset": {"line": 2708, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TFE/build-rot/build-rot-frontend/src/components/ConnectionLine.tsx"], "sourcesContent": ["'use client';\n\ninterface Connection {\n  id: string;\n  fromCardId: string;\n  fromOutput: string;\n  toCardId: string;\n  toInput: string;\n}\n\ninterface CardInstance {\n  id: string;\n  type: string;\n  position: { x: number; y: number };\n  data: Record<string, any>;\n}\n\ninterface ConnectionLineProps {\n  connection: Connection;\n  cards: CardInstance[];\n}\n\nexport default function ConnectionLine({ connection, cards }: ConnectionLineProps) {\n  const fromCard = cards.find(c => c.id === connection.fromCardId);\n  const toCard = cards.find(c => c.id === connection.toCardId);\n\n  if (!fromCard || !toCard) return null;\n\n  // Calculate connection points with better positioning\n  const cardWidth = 192;\n  const cardHeight = 120; // Approximate card height\n\n  const fromX = fromCard.position.x + cardWidth;\n  const fromY = fromCard.position.y + cardHeight / 2;\n  const toX = toCard.position.x;\n  const toY = toCard.position.y + cardHeight / 2;\n\n  // Create a curved path with better curve\n  const controlPointOffset = Math.abs(toX - fromX) * 0.5;\n  const pathData = `M ${fromX} ${fromY} C ${fromX + controlPointOffset} ${fromY}, ${toX - controlPointOffset} ${toY}, ${toX} ${toY}`;\n\n  return (\n    <svg\n      className=\"absolute inset-0 pointer-events-none\"\n      style={{ zIndex: 0 }}\n    >\n      <defs>\n        <marker\n          id={`arrowhead-${connection.id}`}\n          markerWidth=\"10\"\n          markerHeight=\"7\"\n          refX=\"9\"\n          refY=\"3.5\"\n          orient=\"auto\"\n        >\n          <polygon\n            points=\"0 0, 10 3.5, 0 7\"\n            fill=\"#3b82f6\"\n          />\n        </marker>\n      </defs>\n      <path\n        d={pathData}\n        stroke=\"#3b82f6\"\n        strokeWidth=\"2\"\n        fill=\"none\"\n        markerEnd={`url(#arrowhead-${connection.id})`}\n        className=\"drop-shadow-sm\"\n      />\n    </svg>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAsBe,SAAS,eAAe,EAAE,UAAU,EAAE,KAAK,EAAuB;IAC/E,MAAM,WAAW,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,UAAU;IAC/D,MAAM,SAAS,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,QAAQ;IAE3D,IAAI,CAAC,YAAY,CAAC,QAAQ,OAAO;IAEjC,sDAAsD;IACtD,MAAM,YAAY;IAClB,MAAM,aAAa,KAAK,0BAA0B;IAElD,MAAM,QAAQ,SAAS,QAAQ,CAAC,CAAC,GAAG;IACpC,MAAM,QAAQ,SAAS,QAAQ,CAAC,CAAC,GAAG,aAAa;IACjD,MAAM,MAAM,OAAO,QAAQ,CAAC,CAAC;IAC7B,MAAM,MAAM,OAAO,QAAQ,CAAC,CAAC,GAAG,aAAa;IAE7C,yCAAyC;IACzC,MAAM,qBAAqB,KAAK,GAAG,CAAC,MAAM,SAAS;IACnD,MAAM,WAAW,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,EAAE,QAAQ,mBAAmB,CAAC,EAAE,MAAM,EAAE,EAAE,MAAM,mBAAmB,CAAC,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,EAAE,KAAK;IAElI,qBACE,6LAAC;QACC,WAAU;QACV,OAAO;YAAE,QAAQ;QAAE;;0BAEnB,6LAAC;0BACC,cAAA,6LAAC;oBACC,IAAI,CAAC,UAAU,EAAE,WAAW,EAAE,EAAE;oBAChC,aAAY;oBACZ,cAAa;oBACb,MAAK;oBACL,MAAK;oBACL,QAAO;8BAEP,cAAA,6LAAC;wBACC,QAAO;wBACP,MAAK;;;;;;;;;;;;;;;;0BAIX,6LAAC;gBACC,GAAG;gBACH,QAAO;gBACP,aAAY;gBACZ,MAAK;gBACL,WAAW,CAAC,eAAe,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;gBAC7C,WAAU;;;;;;;;;;;;AAIlB;KAjDwB", "debugId": null}}, {"offset": {"line": 2791, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TFE/build-rot/build-rot-frontend/src/components/CardLibrary.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { X, Search } from 'lucide-react';\nimport { getCardsByCategory, CardDefinition } from '../lib/cardDefinitions';\n\ninterface CardLibraryProps {\n  onAddCard: (cardType: string, position: { x: number; y: number }) => void;\n  onClose: () => void;\n}\n\nexport default function CardLibrary({ onAddCard, onClose }: CardLibraryProps) {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState<string>('all');\n  \n  const cardsByCategory = getCardsByCategory();\n  const categories = ['all', ...Object.keys(cardsByCategory)];\n\n  const filteredCards = Object.entries(cardsByCategory).reduce((acc, [category, cards]) => {\n    if (selectedCategory !== 'all' && category !== selectedCategory) return acc;\n    \n    const filtered = cards.filter(card =>\n      card.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      card.description.toLowerCase().includes(searchTerm.toLowerCase())\n    );\n    \n    if (filtered.length > 0) {\n      acc[category] = filtered;\n    }\n    \n    return acc;\n  }, {} as Record<string, CardDefinition[]>);\n\n  const handleCardClick = (cardType: string) => {\n    // Add card at center of viewport\n    onAddCard(cardType, { x: 300, y: 200 });\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] flex flex-col\">\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">Card Library</h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <div className=\"p-6 border-b\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex-1 relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search cards...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              />\n            </div>\n            <select\n              value={selectedCategory}\n              onChange={(e) => setSelectedCategory(e.target.value)}\n              className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n            >\n              {categories.map(category => (\n                <option key={category} value={category}>\n                  {category === 'all' ? 'All Categories' : category}\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n\n        <div className=\"flex-1 overflow-y-auto p-6\">\n          {Object.keys(filteredCards).length === 0 ? (\n            <div className=\"text-center py-12\">\n              <p className=\"text-gray-500\">No cards found matching your search.</p>\n            </div>\n          ) : (\n            <div className=\"space-y-6\">\n              {Object.entries(filteredCards).map(([category, cards]) => (\n                <div key={category}>\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-3\">{category}</h3>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                    {cards.map((card) => (\n                      <button\n                        key={card.type}\n                        onClick={() => handleCardClick(card.type)}\n                        className=\"text-left p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all\"\n                      >\n                        <div className=\"flex items-center mb-2\">\n                          <div className={`p-2 rounded-md ${card.color} text-white mr-3`}>\n                            <card.icon className=\"h-4 w-4\" />\n                          </div>\n                          <h4 className=\"font-medium text-gray-900\">{card.name}</h4>\n                        </div>\n                        <p className=\"text-sm text-gray-600 mb-3\">{card.description}</p>\n                        \n                        <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                          <div className=\"flex items-center space-x-2\">\n                            {card.inputs.length > 0 && (\n                              <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded\">\n                                {card.inputs.length} input{card.inputs.length !== 1 ? 's' : ''}\n                              </span>\n                            )}\n                            {card.outputs.length > 0 && (\n                              <span className=\"bg-green-100 text-green-800 px-2 py-1 rounded\">\n                                {card.outputs.length} output{card.outputs.length !== 1 ? 's' : ''}\n                              </span>\n                            )}\n                          </div>\n                          {card.configurable && (\n                            <span className=\"bg-orange-100 text-orange-800 px-2 py-1 rounded\">\n                              Configurable\n                            </span>\n                          )}\n                        </div>\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n        <div className=\"p-6 border-t bg-gray-50\">\n          <div className=\"flex items-center justify-between text-sm text-gray-600\">\n            <div>\n              <strong>Tip:</strong> Drag cards from here to your canvas, then connect matching input/output types\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-blue-500 rounded-full mr-1\"></div>\n                <span>Input</span>\n              </div>\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-green-500 rounded-full mr-1\"></div>\n                <span>Output</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAWe,SAAS,YAAY,EAAE,SAAS,EAAE,OAAO,EAAoB;;IAC1E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,MAAM,kBAAkB,CAAA,GAAA,gIAAA,CAAA,qBAAkB,AAAD;IACzC,MAAM,aAAa;QAAC;WAAU,OAAO,IAAI,CAAC;KAAiB;IAE3D,MAAM,gBAAgB,OAAO,OAAO,CAAC,iBAAiB,MAAM,CAAC,CAAC,KAAK,CAAC,UAAU,MAAM;QAClF,IAAI,qBAAqB,SAAS,aAAa,kBAAkB,OAAO;QAExE,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA,OAC5B,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAGhE,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,GAAG,CAAC,SAAS,GAAG;QAClB;QAEA,OAAO;IACT,GAAG,CAAC;IAEJ,MAAM,kBAAkB,CAAC;QACvB,iCAAiC;QACjC,UAAU,UAAU;YAAE,GAAG;YAAK,GAAG;QAAI;IACvC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAGd,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACnD,WAAU;0CAET,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;wCAAsB,OAAO;kDAC3B,aAAa,QAAQ,mBAAmB;uCAD9B;;;;;;;;;;;;;;;;;;;;;8BAQrB,6LAAC;oBAAI,WAAU;8BACZ,OAAO,IAAI,CAAC,eAAe,MAAM,KAAK,kBACrC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;6CAG/B,6LAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,UAAU,MAAM,iBACnD,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,6LAAC;wCAAI,WAAU;kDACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;gDAEC,SAAS,IAAM,gBAAgB,KAAK,IAAI;gDACxC,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAW,CAAC,eAAe,EAAE,KAAK,KAAK,CAAC,gBAAgB,CAAC;0EAC5D,cAAA,6LAAC,KAAK,IAAI;oEAAC,WAAU;;;;;;;;;;;0EAEvB,6LAAC;gEAAG,WAAU;0EAA6B,KAAK,IAAI;;;;;;;;;;;;kEAEtD,6LAAC;wDAAE,WAAU;kEAA8B,KAAK,WAAW;;;;;;kEAE3D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;oEACZ,KAAK,MAAM,CAAC,MAAM,GAAG,mBACpB,6LAAC;wEAAK,WAAU;;4EACb,KAAK,MAAM,CAAC,MAAM;4EAAC;4EAAO,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI,MAAM;;;;;;;oEAG/D,KAAK,OAAO,CAAC,MAAM,GAAG,mBACrB,6LAAC;wEAAK,WAAU;;4EACb,KAAK,OAAO,CAAC,MAAM;4EAAC;4EAAQ,KAAK,OAAO,CAAC,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;4DAIpE,KAAK,YAAY,kBAChB,6LAAC;gEAAK,WAAU;0EAAkD;;;;;;;;;;;;;+CA1BjE,KAAK,IAAI;;;;;;;;;;;+BALZ;;;;;;;;;;;;;;;8BA6ClB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;kDAAO;;;;;;oCAAa;;;;;;;0CAEvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;GA3IwB;KAAA", "debugId": null}}, {"offset": {"line": 3188, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TFE/build-rot/build-rot-frontend/src/lib/codeGenerator.ts"], "sourcesContent": ["interface CardInstance {\n  id: string;\n  type: string;\n  position: { x: number; y: number };\n  data: Record<string, any>;\n}\n\ninterface Connection {\n  id: string;\n  fromCardId: string;\n  fromOutput: string;\n  toCardId: string;\n  toInput: string;\n}\n\nexport function generateCodeFromFlow(\n  cards: CardInstance[], \n  connections: Connection[], \n  variables: Record<string, string>\n): string {\n  let code = `// Generated code from Visual Builder\\n\\n`;\n  \n  // Add variables\n  Object.entries(variables).forEach(([key, value]) => {\n    code += `const ${key} = '${value}';\\n`;\n  });\n  code += '\\n';\n\n  // Find event cards (cards with no inputs)\n  const eventCards = cards.filter(card => {\n    const hasInputConnections = connections.some(conn => conn.toCardId === card.id);\n    return !hasInputConnections && ['message_received', 'member_join'].includes(card.type);\n  });\n\n  // Generate code for each event flow\n  eventCards.forEach(eventCard => {\n    code += generateEventFlow(eventCard, cards, connections, variables);\n    code += '\\n';\n  });\n\n  return code;\n}\n\nfunction generateEventFlow(\n  eventCard: CardInstance,\n  cards: CardInstance[],\n  connections: Connection[],\n  variables: Record<string, string>\n): string {\n  let code = '';\n\n  switch (eventCard.type) {\n    case 'message_received':\n      code += generateMessageReceivedFlow(eventCard, cards, connections, variables);\n      break;\n    case 'member_join':\n      code += generateMemberJoinFlow(eventCard, cards, connections, variables);\n      break;\n  }\n\n  return code;\n}\n\nfunction generateMessageReceivedFlow(\n  eventCard: CardInstance,\n  cards: CardInstance[],\n  connections: Connection[],\n  variables: Record<string, string>\n): string {\n  let code = 'bot.onMessage((message) => {\\n';\n\n  // Add trigger condition if specified\n  if (eventCard.data.trigger && eventCard.data.trigger !== 'all' && eventCard.data.triggerValue) {\n    const triggerValue = replaceVariables(eventCard.data.triggerValue, variables);\n\n    switch (eventCard.data.trigger) {\n      case 'startsWith':\n        code += `    if (!message.content.startsWith('${triggerValue}')) return;\\n`;\n        break;\n      case 'contains':\n        code += `    if (!message.content.includes('${triggerValue}')) return;\\n`;\n        break;\n      case 'exact':\n        code += `    if (message.content !== '${triggerValue}') return;\\n`;\n        break;\n    }\n    code += '\\n';\n  }\n\n  // Extract variables from message event\n  code += '    // Extract variables from message event\\n';\n  code += '    const author_name = message.author.username;\\n';\n  code += '    const author_id = message.author.id;\\n';\n  code += '    const content = message.content;\\n';\n  code += '    const channel_name = message.channel.name;\\n';\n  code += '    const guild_name = message.guild ? message.guild.name : \"DM\";\\n';\n  code += '\\n';\n\n  // Generate code for connected cards\n  const connectedCards = getConnectedCards(eventCard.id, connections, cards);\n  connectedCards.forEach(card => {\n    code += generateCardCode(card, cards, connections, variables, '    ');\n  });\n\n  code += '});\\n';\n  return code;\n}\n\nfunction generateMemberJoinFlow(\n  eventCard: CardInstance,\n  cards: CardInstance[],\n  connections: Connection[],\n  variables: Record<string, string>\n): string {\n  let code = 'bot.onMemberJoin((member) => {\\n';\n\n  // Extract variables from member join event\n  code += '    // Extract variables from member join event\\n';\n  code += '    const username = member.user.username;\\n';\n  code += '    const user_id = member.user.id;\\n';\n  code += '    const display_name = member.displayName || member.user.username;\\n';\n  code += '    const guild_name = member.guild.name;\\n';\n  code += '    const member_count = member.guild.memberCount;\\n';\n  code += '\\n';\n\n  const connectedCards = getConnectedCards(eventCard.id, connections, cards);\n  connectedCards.forEach(card => {\n    code += generateCardCode(card, cards, connections, variables, '    ');\n  });\n\n  code += '});\\n';\n  return code;\n}\n\nfunction generateCardCode(\n  card: CardInstance,\n  cards: CardInstance[],\n  connections: Connection[],\n  variables: Record<string, string>,\n  indent: string = ''\n): string {\n  let code = '';\n\n  switch (card.type) {\n    case 'send_message':\n      code += generateSendMessageCode(card, variables, indent);\n      break;\n    case 'reply_message':\n      code += generateReplyMessageCode(card, variables, indent);\n      break;\n    case 'get_channel':\n      code += generateGetChannelCode(card, variables, indent);\n      break;\n    case 'text_format':\n      code += generateTextFormatCode(card, variables, indent);\n      break;\n    case 'http_get':\n      code += generateHttpGetCode(card, variables, indent);\n      break;\n    case 'http_post':\n      code += generateHttpPostCode(card, variables, indent);\n      break;\n    case 'http_put':\n      code += generateHttpPutCode(card, variables, indent);\n      break;\n    case 'http_delete':\n      code += generateHttpDeleteCode(card, variables, indent);\n      break;\n    case 'condition':\n      code += generateConditionCode(card, cards, connections, variables, indent);\n      break;\n    case 'delay':\n      code += generateDelayCode(card, variables, indent);\n      break;\n    case 'variable_set':\n      code += generateVariableSetCode(card, variables, indent);\n      break;\n  }\n\n  return code;\n}\n\nfunction generateSendMessageCode(card: CardInstance, variables: Record<string, string>, indent: string): string {\n  const content = replaceVariables(card.data.content || 'Hello!', variables);\n  const channelName = card.data.channelName || 'general';\n\n  let code = `${indent}// Send message\\n`;\n  if (card.data.channelName) {\n    code += `${indent}const targetChannel = message.guild.channels.cache.find(ch => ch.name === '${channelName}') || message.channel;\\n`;\n    code += `${indent}await bot.sendMessage(targetChannel, '${content}');\\n\\n`;\n  } else {\n    code += `${indent}await bot.sendMessage(message.channel, '${content}');\\n\\n`;\n  }\n\n  return code;\n}\n\nfunction generateReplyMessageCode(card: CardInstance, variables: Record<string, string>, indent: string): string {\n  const content = replaceVariables(card.data.content || 'Thanks for your message!', variables);\n  return `${indent}// Reply to message\\n${indent}await message.reply('${content}');\\n\\n`;\n}\n\nfunction generateGetChannelCode(card: CardInstance, variables: Record<string, string>, indent: string): string {\n  const channelName = replaceVariables(card.data.channelName || 'general', variables);\n\n  let code = `${indent}// Get channel\\n`;\n  code += `${indent}const targetChannel = message.guild.channels.cache.find(ch => ch.name === '${channelName}');\\n`;\n  code += `${indent}const channel_name = targetChannel ? targetChannel.name : null;\\n`;\n  code += `${indent}const channel_id = targetChannel ? targetChannel.id : null;\\n`;\n  code += `${indent}const found = !!targetChannel;\\n\\n`;\n\n  return code;\n}\n\nfunction generateTextFormatCode(card: CardInstance, variables: Record<string, string>, indent: string): string {\n  const template = replaceVariables(card.data.template || 'Hello {{username}}!', variables);\n\n  let code = `${indent}// Format text\\n`;\n  code += `${indent}const formatted_text = \\`${template}\\`;\\n\\n`;\n\n  return code;\n}\n\nfunction generateHttpGetCode(card: CardInstance, variables: Record<string, string>, indent: string): string {\n  const url = replaceVariables(card.data.url || '', variables);\n  const headers = card.data.headers ? JSON.parse(card.data.headers) : {};\n  \n  let code = `${indent}// HTTP GET request\\n`;\n  code += `${indent}try {\\n`;\n  code += `${indent}    const response = await fetch('${url}', {\\n`;\n  code += `${indent}        method: 'GET',\\n`;\n  if (Object.keys(headers).length > 0) {\n    code += `${indent}        headers: ${JSON.stringify(headers, null, 8).replace(/\\n/g, '\\n' + indent)},\\n`;\n  }\n  code += `${indent}    });\\n`;\n  code += `${indent}    const data = await response.json();\\n`;\n  code += `${indent}    console.log('GET response:', data);\\n`;\n  code += `${indent}} catch (error) {\\n`;\n  code += `${indent}    console.error('GET request failed:', error);\\n`;\n  code += `${indent}}\\n\\n`;\n  \n  return code;\n}\n\nfunction generateHttpPostCode(card: CardInstance, variables: Record<string, string>, indent: string): string {\n  const url = replaceVariables(card.data.url || '', variables);\n  const body = replaceVariables(card.data.body || '{}', variables);\n  const headers = card.data.headers ? JSON.parse(card.data.headers) : { 'Content-Type': 'application/json' };\n  \n  let code = `${indent}// HTTP POST request\\n`;\n  code += `${indent}try {\\n`;\n  code += `${indent}    const response = await fetch('${url}', {\\n`;\n  code += `${indent}        method: 'POST',\\n`;\n  code += `${indent}        headers: ${JSON.stringify(headers, null, 8).replace(/\\n/g, '\\n' + indent)},\\n`;\n  code += `${indent}        body: JSON.stringify(${body})\\n`;\n  code += `${indent}    });\\n`;\n  code += `${indent}    const data = await response.json();\\n`;\n  code += `${indent}    console.log('POST response:', data);\\n`;\n  code += `${indent}} catch (error) {\\n`;\n  code += `${indent}    console.error('POST request failed:', error);\\n`;\n  code += `${indent}}\\n\\n`;\n  \n  return code;\n}\n\nfunction generateHttpPutCode(card: CardInstance, variables: Record<string, string>, indent: string): string {\n  const url = replaceVariables(card.data.url || '', variables);\n  const body = replaceVariables(card.data.body || '{}', variables);\n  const headers = card.data.headers ? JSON.parse(card.data.headers) : { 'Content-Type': 'application/json' };\n  \n  let code = `${indent}// HTTP PUT request\\n`;\n  code += `${indent}try {\\n`;\n  code += `${indent}    const response = await fetch('${url}', {\\n`;\n  code += `${indent}        method: 'PUT',\\n`;\n  code += `${indent}        headers: ${JSON.stringify(headers, null, 8).replace(/\\n/g, '\\n' + indent)},\\n`;\n  code += `${indent}        body: JSON.stringify(${body})\\n`;\n  code += `${indent}    });\\n`;\n  code += `${indent}    const data = await response.json();\\n`;\n  code += `${indent}    console.log('PUT response:', data);\\n`;\n  code += `${indent}} catch (error) {\\n`;\n  code += `${indent}    console.error('PUT request failed:', error);\\n`;\n  code += `${indent}}\\n\\n`;\n  \n  return code;\n}\n\nfunction generateHttpDeleteCode(card: CardInstance, variables: Record<string, string>, indent: string): string {\n  const url = replaceVariables(card.data.url || '', variables);\n  const headers = card.data.headers ? JSON.parse(card.data.headers) : {};\n  \n  let code = `${indent}// HTTP DELETE request\\n`;\n  code += `${indent}try {\\n`;\n  code += `${indent}    const response = await fetch('${url}', {\\n`;\n  code += `${indent}        method: 'DELETE',\\n`;\n  if (Object.keys(headers).length > 0) {\n    code += `${indent}        headers: ${JSON.stringify(headers, null, 8).replace(/\\n/g, '\\n' + indent)},\\n`;\n  }\n  code += `${indent}    });\\n`;\n  code += `${indent}    console.log('DELETE response status:', response.status);\\n`;\n  code += `${indent}} catch (error) {\\n`;\n  code += `${indent}    console.error('DELETE request failed:', error);\\n`;\n  code += `${indent}}\\n\\n`;\n  \n  return code;\n}\n\nfunction generateConditionCode(\n  card: CardInstance,\n  cards: CardInstance[],\n  connections: Connection[],\n  variables: Record<string, string>,\n  indent: string\n): string {\n  const operator = card.data.operator || 'equals';\n  const value1 = replaceVariables(card.data.value1 || '', variables);\n  const value2 = replaceVariables(card.data.value2 || '', variables);\n  \n  let condition = '';\n  switch (operator) {\n    case 'equals':\n      condition = `${value1} === ${value2}`;\n      break;\n    case 'notEquals':\n      condition = `${value1} !== ${value2}`;\n      break;\n    case 'contains':\n      condition = `${value1}.includes(${value2})`;\n      break;\n    case 'startsWith':\n      condition = `${value1}.startsWith(${value2})`;\n      break;\n    case 'greaterThan':\n      condition = `${value1} > ${value2}`;\n      break;\n    case 'lessThan':\n      condition = `${value1} < ${value2}`;\n      break;\n  }\n  \n  let code = `${indent}// Condition check\\n`;\n  code += `${indent}if (${condition}) {\\n`;\n  \n  // Generate code for true branch\n  const trueConnections = connections.filter(conn => conn.fromCardId === card.id && conn.fromOutput === 'true');\n  trueConnections.forEach(conn => {\n    const connectedCard = cards.find(c => c.id === conn.toCardId);\n    if (connectedCard) {\n      code += generateCardCode(connectedCard, cards, connections, variables, indent + '    ');\n    }\n  });\n  \n  code += `${indent}} else {\\n`;\n  \n  // Generate code for false branch\n  const falseConnections = connections.filter(conn => conn.fromCardId === card.id && conn.fromOutput === 'false');\n  falseConnections.forEach(conn => {\n    const connectedCard = cards.find(c => c.id === conn.toCardId);\n    if (connectedCard) {\n      code += generateCardCode(connectedCard, cards, connections, variables, indent + '    ');\n    }\n  });\n  \n  code += `${indent}}\\n\\n`;\n  return code;\n}\n\nfunction generateDelayCode(card: CardInstance, variables: Record<string, string>, indent: string): string {\n  const duration = parseInt(card.data.duration || '1') * 1000;\n  return `${indent}// Delay\\n${indent}await new Promise(resolve => setTimeout(resolve, ${duration}));\\n\\n`;\n}\n\nfunction generateVariableSetCode(card: CardInstance, variables: Record<string, string>, indent: string): string {\n  const varName = card.data.variableName || 'myVariable';\n  const value = replaceVariables(card.data.value || '', variables);\n  return `${indent}// Set variable\\n${indent}const ${varName} = '${value}';\\n\\n`;\n}\n\nfunction getConnectedCards(cardId: string, connections: Connection[], cards: CardInstance[]): CardInstance[] {\n  const connectedCardIds = connections\n    .filter(conn => conn.fromCardId === cardId)\n    .map(conn => conn.toCardId);\n  \n  return cards.filter(card => connectedCardIds.includes(card.id));\n}\n\nfunction replaceVariables(text: string, variables: Record<string, string>): string {\n  let result = text;\n\n  // Replace template variables like {{BOT_ID}}\n  Object.entries(variables).forEach(([key, value]) => {\n    result = result.replace(new RegExp(`{{${key}}}`, 'g'), value);\n  });\n\n  // Replace common Discord.js variables with template literals\n  result = result.replace(/{{author}}/g, '${author_name}');\n  result = result.replace(/{{author_name}}/g, '${author_name}');\n  result = result.replace(/{{author\\.id}}/g, '${author_id}');\n  result = result.replace(/{{author_id}}/g, '${author_id}');\n  result = result.replace(/{{content}}/g, '${content}');\n  result = result.replace(/{{channel}}/g, '${channel_name}');\n  result = result.replace(/{{channel_name}}/g, '${channel_name}');\n  result = result.replace(/{{guild}}/g, '${guild_name}');\n  result = result.replace(/{{guild_name}}/g, '${guild_name}');\n  result = result.replace(/{{username}}/g, '${username}');\n  result = result.replace(/{{user_id}}/g, '${user_id}');\n  result = result.replace(/{{display_name}}/g, '${display_name}');\n  result = result.replace(/{{member_count}}/g, '${member_count}');\n  result = result.replace(/{{member\\.username}}/g, '${username}');\n  result = result.replace(/{{member\\.id}}/g, '${user_id}');\n  result = result.replace(/{{custom_value}}/g, '${custom_value || \"\"}');\n\n  return result;\n}\n"], "names": [], "mappings": ";;;AAeO,SAAS,qBACd,KAAqB,EACrB,WAAyB,EACzB,SAAiC;IAEjC,IAAI,OAAO,CAAC,yCAAyC,CAAC;IAEtD,gBAAgB;IAChB,OAAO,OAAO,CAAC,WAAW,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC7C,QAAQ,CAAC,MAAM,EAAE,IAAI,IAAI,EAAE,MAAM,IAAI,CAAC;IACxC;IACA,QAAQ;IAER,0CAA0C;IAC1C,MAAM,aAAa,MAAM,MAAM,CAAC,CAAA;QAC9B,MAAM,sBAAsB,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,KAAK,EAAE;QAC9E,OAAO,CAAC,uBAAuB;YAAC;YAAoB;SAAc,CAAC,QAAQ,CAAC,KAAK,IAAI;IACvF;IAEA,oCAAoC;IACpC,WAAW,OAAO,CAAC,CAAA;QACjB,QAAQ,kBAAkB,WAAW,OAAO,aAAa;QACzD,QAAQ;IACV;IAEA,OAAO;AACT;AAEA,SAAS,kBACP,SAAuB,EACvB,KAAqB,EACrB,WAAyB,EACzB,SAAiC;IAEjC,IAAI,OAAO;IAEX,OAAQ,UAAU,IAAI;QACpB,KAAK;YACH,QAAQ,4BAA4B,WAAW,OAAO,aAAa;YACnE;QACF,KAAK;YACH,QAAQ,uBAAuB,WAAW,OAAO,aAAa;YAC9D;IACJ;IAEA,OAAO;AACT;AAEA,SAAS,4BACP,SAAuB,EACvB,KAAqB,EACrB,WAAyB,EACzB,SAAiC;IAEjC,IAAI,OAAO;IAEX,qCAAqC;IACrC,IAAI,UAAU,IAAI,CAAC,OAAO,IAAI,UAAU,IAAI,CAAC,OAAO,KAAK,SAAS,UAAU,IAAI,CAAC,YAAY,EAAE;QAC7F,MAAM,eAAe,iBAAiB,UAAU,IAAI,CAAC,YAAY,EAAE;QAEnE,OAAQ,UAAU,IAAI,CAAC,OAAO;YAC5B,KAAK;gBACH,QAAQ,CAAC,qCAAqC,EAAE,aAAa,aAAa,CAAC;gBAC3E;YACF,KAAK;gBACH,QAAQ,CAAC,mCAAmC,EAAE,aAAa,aAAa,CAAC;gBACzE;YACF,KAAK;gBACH,QAAQ,CAAC,6BAA6B,EAAE,aAAa,YAAY,CAAC;gBAClE;QACJ;QACA,QAAQ;IACV;IAEA,uCAAuC;IACvC,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IAER,oCAAoC;IACpC,MAAM,iBAAiB,kBAAkB,UAAU,EAAE,EAAE,aAAa;IACpE,eAAe,OAAO,CAAC,CAAA;QACrB,QAAQ,iBAAiB,MAAM,OAAO,aAAa,WAAW;IAChE;IAEA,QAAQ;IACR,OAAO;AACT;AAEA,SAAS,uBACP,SAAuB,EACvB,KAAqB,EACrB,WAAyB,EACzB,SAAiC;IAEjC,IAAI,OAAO;IAEX,2CAA2C;IAC3C,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IAER,MAAM,iBAAiB,kBAAkB,UAAU,EAAE,EAAE,aAAa;IACpE,eAAe,OAAO,CAAC,CAAA;QACrB,QAAQ,iBAAiB,MAAM,OAAO,aAAa,WAAW;IAChE;IAEA,QAAQ;IACR,OAAO;AACT;AAEA,SAAS,iBACP,IAAkB,EAClB,KAAqB,EACrB,WAAyB,EACzB,SAAiC,EACjC,SAAiB,EAAE;IAEnB,IAAI,OAAO;IAEX,OAAQ,KAAK,IAAI;QACf,KAAK;YACH,QAAQ,wBAAwB,MAAM,WAAW;YACjD;QACF,KAAK;YACH,QAAQ,yBAAyB,MAAM,WAAW;YAClD;QACF,KAAK;YACH,QAAQ,uBAAuB,MAAM,WAAW;YAChD;QACF,KAAK;YACH,QAAQ,uBAAuB,MAAM,WAAW;YAChD;QACF,KAAK;YACH,QAAQ,oBAAoB,MAAM,WAAW;YAC7C;QACF,KAAK;YACH,QAAQ,qBAAqB,MAAM,WAAW;YAC9C;QACF,KAAK;YACH,QAAQ,oBAAoB,MAAM,WAAW;YAC7C;QACF,KAAK;YACH,QAAQ,uBAAuB,MAAM,WAAW;YAChD;QACF,KAAK;YACH,QAAQ,sBAAsB,MAAM,OAAO,aAAa,WAAW;YACnE;QACF,KAAK;YACH,QAAQ,kBAAkB,MAAM,WAAW;YAC3C;QACF,KAAK;YACH,QAAQ,wBAAwB,MAAM,WAAW;YACjD;IACJ;IAEA,OAAO;AACT;AAEA,SAAS,wBAAwB,IAAkB,EAAE,SAAiC,EAAE,MAAc;IACpG,MAAM,UAAU,iBAAiB,KAAK,IAAI,CAAC,OAAO,IAAI,UAAU;IAChE,MAAM,cAAc,KAAK,IAAI,CAAC,WAAW,IAAI;IAE7C,IAAI,OAAO,GAAG,OAAO,iBAAiB,CAAC;IACvC,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE;QACzB,QAAQ,GAAG,OAAO,2EAA2E,EAAE,YAAY,wBAAwB,CAAC;QACpI,QAAQ,GAAG,OAAO,sCAAsC,EAAE,QAAQ,OAAO,CAAC;IAC5E,OAAO;QACL,QAAQ,GAAG,OAAO,wCAAwC,EAAE,QAAQ,OAAO,CAAC;IAC9E;IAEA,OAAO;AACT;AAEA,SAAS,yBAAyB,IAAkB,EAAE,SAAiC,EAAE,MAAc;IACrG,MAAM,UAAU,iBAAiB,KAAK,IAAI,CAAC,OAAO,IAAI,4BAA4B;IAClF,OAAO,GAAG,OAAO,qBAAqB,EAAE,OAAO,qBAAqB,EAAE,QAAQ,OAAO,CAAC;AACxF;AAEA,SAAS,uBAAuB,IAAkB,EAAE,SAAiC,EAAE,MAAc;IACnG,MAAM,cAAc,iBAAiB,KAAK,IAAI,CAAC,WAAW,IAAI,WAAW;IAEzE,IAAI,OAAO,GAAG,OAAO,gBAAgB,CAAC;IACtC,QAAQ,GAAG,OAAO,2EAA2E,EAAE,YAAY,KAAK,CAAC;IACjH,QAAQ,GAAG,OAAO,iEAAiE,CAAC;IACpF,QAAQ,GAAG,OAAO,6DAA6D,CAAC;IAChF,QAAQ,GAAG,OAAO,kCAAkC,CAAC;IAErD,OAAO;AACT;AAEA,SAAS,uBAAuB,IAAkB,EAAE,SAAiC,EAAE,MAAc;IACnG,MAAM,WAAW,iBAAiB,KAAK,IAAI,CAAC,QAAQ,IAAI,uBAAuB;IAE/E,IAAI,OAAO,GAAG,OAAO,gBAAgB,CAAC;IACtC,QAAQ,GAAG,OAAO,yBAAyB,EAAE,SAAS,OAAO,CAAC;IAE9D,OAAO;AACT;AAEA,SAAS,oBAAoB,IAAkB,EAAE,SAAiC,EAAE,MAAc;IAChG,MAAM,MAAM,iBAAiB,KAAK,IAAI,CAAC,GAAG,IAAI,IAAI;IAClD,MAAM,UAAU,KAAK,IAAI,CAAC,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,OAAO,IAAI,CAAC;IAErE,IAAI,OAAO,GAAG,OAAO,qBAAqB,CAAC;IAC3C,QAAQ,GAAG,OAAO,OAAO,CAAC;IAC1B,QAAQ,GAAG,OAAO,kCAAkC,EAAE,IAAI,MAAM,CAAC;IACjE,QAAQ,GAAG,OAAO,wBAAwB,CAAC;IAC3C,IAAI,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG,GAAG;QACnC,QAAQ,GAAG,OAAO,iBAAiB,EAAE,KAAK,SAAS,CAAC,SAAS,MAAM,GAAG,OAAO,CAAC,OAAO,OAAO,QAAQ,GAAG,CAAC;IAC1G;IACA,QAAQ,GAAG,OAAO,SAAS,CAAC;IAC5B,QAAQ,GAAG,OAAO,yCAAyC,CAAC;IAC5D,QAAQ,GAAG,OAAO,yCAAyC,CAAC;IAC5D,QAAQ,GAAG,OAAO,mBAAmB,CAAC;IACtC,QAAQ,GAAG,OAAO,kDAAkD,CAAC;IACrE,QAAQ,GAAG,OAAO,KAAK,CAAC;IAExB,OAAO;AACT;AAEA,SAAS,qBAAqB,IAAkB,EAAE,SAAiC,EAAE,MAAc;IACjG,MAAM,MAAM,iBAAiB,KAAK,IAAI,CAAC,GAAG,IAAI,IAAI;IAClD,MAAM,OAAO,iBAAiB,KAAK,IAAI,CAAC,IAAI,IAAI,MAAM;IACtD,MAAM,UAAU,KAAK,IAAI,CAAC,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,OAAO,IAAI;QAAE,gBAAgB;IAAmB;IAEzG,IAAI,OAAO,GAAG,OAAO,sBAAsB,CAAC;IAC5C,QAAQ,GAAG,OAAO,OAAO,CAAC;IAC1B,QAAQ,GAAG,OAAO,kCAAkC,EAAE,IAAI,MAAM,CAAC;IACjE,QAAQ,GAAG,OAAO,yBAAyB,CAAC;IAC5C,QAAQ,GAAG,OAAO,iBAAiB,EAAE,KAAK,SAAS,CAAC,SAAS,MAAM,GAAG,OAAO,CAAC,OAAO,OAAO,QAAQ,GAAG,CAAC;IACxG,QAAQ,GAAG,OAAO,6BAA6B,EAAE,KAAK,GAAG,CAAC;IAC1D,QAAQ,GAAG,OAAO,SAAS,CAAC;IAC5B,QAAQ,GAAG,OAAO,yCAAyC,CAAC;IAC5D,QAAQ,GAAG,OAAO,0CAA0C,CAAC;IAC7D,QAAQ,GAAG,OAAO,mBAAmB,CAAC;IACtC,QAAQ,GAAG,OAAO,mDAAmD,CAAC;IACtE,QAAQ,GAAG,OAAO,KAAK,CAAC;IAExB,OAAO;AACT;AAEA,SAAS,oBAAoB,IAAkB,EAAE,SAAiC,EAAE,MAAc;IAChG,MAAM,MAAM,iBAAiB,KAAK,IAAI,CAAC,GAAG,IAAI,IAAI;IAClD,MAAM,OAAO,iBAAiB,KAAK,IAAI,CAAC,IAAI,IAAI,MAAM;IACtD,MAAM,UAAU,KAAK,IAAI,CAAC,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,OAAO,IAAI;QAAE,gBAAgB;IAAmB;IAEzG,IAAI,OAAO,GAAG,OAAO,qBAAqB,CAAC;IAC3C,QAAQ,GAAG,OAAO,OAAO,CAAC;IAC1B,QAAQ,GAAG,OAAO,kCAAkC,EAAE,IAAI,MAAM,CAAC;IACjE,QAAQ,GAAG,OAAO,wBAAwB,CAAC;IAC3C,QAAQ,GAAG,OAAO,iBAAiB,EAAE,KAAK,SAAS,CAAC,SAAS,MAAM,GAAG,OAAO,CAAC,OAAO,OAAO,QAAQ,GAAG,CAAC;IACxG,QAAQ,GAAG,OAAO,6BAA6B,EAAE,KAAK,GAAG,CAAC;IAC1D,QAAQ,GAAG,OAAO,SAAS,CAAC;IAC5B,QAAQ,GAAG,OAAO,yCAAyC,CAAC;IAC5D,QAAQ,GAAG,OAAO,yCAAyC,CAAC;IAC5D,QAAQ,GAAG,OAAO,mBAAmB,CAAC;IACtC,QAAQ,GAAG,OAAO,kDAAkD,CAAC;IACrE,QAAQ,GAAG,OAAO,KAAK,CAAC;IAExB,OAAO;AACT;AAEA,SAAS,uBAAuB,IAAkB,EAAE,SAAiC,EAAE,MAAc;IACnG,MAAM,MAAM,iBAAiB,KAAK,IAAI,CAAC,GAAG,IAAI,IAAI;IAClD,MAAM,UAAU,KAAK,IAAI,CAAC,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,OAAO,IAAI,CAAC;IAErE,IAAI,OAAO,GAAG,OAAO,wBAAwB,CAAC;IAC9C,QAAQ,GAAG,OAAO,OAAO,CAAC;IAC1B,QAAQ,GAAG,OAAO,kCAAkC,EAAE,IAAI,MAAM,CAAC;IACjE,QAAQ,GAAG,OAAO,2BAA2B,CAAC;IAC9C,IAAI,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG,GAAG;QACnC,QAAQ,GAAG,OAAO,iBAAiB,EAAE,KAAK,SAAS,CAAC,SAAS,MAAM,GAAG,OAAO,CAAC,OAAO,OAAO,QAAQ,GAAG,CAAC;IAC1G;IACA,QAAQ,GAAG,OAAO,SAAS,CAAC;IAC5B,QAAQ,GAAG,OAAO,8DAA8D,CAAC;IACjF,QAAQ,GAAG,OAAO,mBAAmB,CAAC;IACtC,QAAQ,GAAG,OAAO,qDAAqD,CAAC;IACxE,QAAQ,GAAG,OAAO,KAAK,CAAC;IAExB,OAAO;AACT;AAEA,SAAS,sBACP,IAAkB,EAClB,KAAqB,EACrB,WAAyB,EACzB,SAAiC,EACjC,MAAc;IAEd,MAAM,WAAW,KAAK,IAAI,CAAC,QAAQ,IAAI;IACvC,MAAM,SAAS,iBAAiB,KAAK,IAAI,CAAC,MAAM,IAAI,IAAI;IACxD,MAAM,SAAS,iBAAiB,KAAK,IAAI,CAAC,MAAM,IAAI,IAAI;IAExD,IAAI,YAAY;IAChB,OAAQ;QACN,KAAK;YACH,YAAY,GAAG,OAAO,KAAK,EAAE,QAAQ;YACrC;QACF,KAAK;YACH,YAAY,GAAG,OAAO,KAAK,EAAE,QAAQ;YACrC;QACF,KAAK;YACH,YAAY,GAAG,OAAO,UAAU,EAAE,OAAO,CAAC,CAAC;YAC3C;QACF,KAAK;YACH,YAAY,GAAG,OAAO,YAAY,EAAE,OAAO,CAAC,CAAC;YAC7C;QACF,KAAK;YACH,YAAY,GAAG,OAAO,GAAG,EAAE,QAAQ;YACnC;QACF,KAAK;YACH,YAAY,GAAG,OAAO,GAAG,EAAE,QAAQ;YACnC;IACJ;IAEA,IAAI,OAAO,GAAG,OAAO,oBAAoB,CAAC;IAC1C,QAAQ,GAAG,OAAO,IAAI,EAAE,UAAU,KAAK,CAAC;IAExC,gCAAgC;IAChC,MAAM,kBAAkB,YAAY,MAAM,CAAC,CAAA,OAAQ,KAAK,UAAU,KAAK,KAAK,EAAE,IAAI,KAAK,UAAU,KAAK;IACtG,gBAAgB,OAAO,CAAC,CAAA;QACtB,MAAM,gBAAgB,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,QAAQ;QAC5D,IAAI,eAAe;YACjB,QAAQ,iBAAiB,eAAe,OAAO,aAAa,WAAW,SAAS;QAClF;IACF;IAEA,QAAQ,GAAG,OAAO,UAAU,CAAC;IAE7B,iCAAiC;IACjC,MAAM,mBAAmB,YAAY,MAAM,CAAC,CAAA,OAAQ,KAAK,UAAU,KAAK,KAAK,EAAE,IAAI,KAAK,UAAU,KAAK;IACvG,iBAAiB,OAAO,CAAC,CAAA;QACvB,MAAM,gBAAgB,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,QAAQ;QAC5D,IAAI,eAAe;YACjB,QAAQ,iBAAiB,eAAe,OAAO,aAAa,WAAW,SAAS;QAClF;IACF;IAEA,QAAQ,GAAG,OAAO,KAAK,CAAC;IACxB,OAAO;AACT;AAEA,SAAS,kBAAkB,IAAkB,EAAE,SAAiC,EAAE,MAAc;IAC9F,MAAM,WAAW,SAAS,KAAK,IAAI,CAAC,QAAQ,IAAI,OAAO;IACvD,OAAO,GAAG,OAAO,UAAU,EAAE,OAAO,iDAAiD,EAAE,SAAS,OAAO,CAAC;AAC1G;AAEA,SAAS,wBAAwB,IAAkB,EAAE,SAAiC,EAAE,MAAc;IACpG,MAAM,UAAU,KAAK,IAAI,CAAC,YAAY,IAAI;IAC1C,MAAM,QAAQ,iBAAiB,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI;IACtD,OAAO,GAAG,OAAO,iBAAiB,EAAE,OAAO,MAAM,EAAE,QAAQ,IAAI,EAAE,MAAM,MAAM,CAAC;AAChF;AAEA,SAAS,kBAAkB,MAAc,EAAE,WAAyB,EAAE,KAAqB;IACzF,MAAM,mBAAmB,YACtB,MAAM,CAAC,CAAA,OAAQ,KAAK,UAAU,KAAK,QACnC,GAAG,CAAC,CAAA,OAAQ,KAAK,QAAQ;IAE5B,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,iBAAiB,QAAQ,CAAC,KAAK,EAAE;AAC/D;AAEA,SAAS,iBAAiB,IAAY,EAAE,SAAiC;IACvE,IAAI,SAAS;IAEb,6CAA6C;IAC7C,OAAO,OAAO,CAAC,WAAW,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC7C,SAAS,OAAO,OAAO,CAAC,IAAI,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM;IACzD;IAEA,6DAA6D;IAC7D,SAAS,OAAO,OAAO,CAAC,eAAe;IACvC,SAAS,OAAO,OAAO,CAAC,oBAAoB;IAC5C,SAAS,OAAO,OAAO,CAAC,mBAAmB;IAC3C,SAAS,OAAO,OAAO,CAAC,kBAAkB;IAC1C,SAAS,OAAO,OAAO,CAAC,gBAAgB;IACxC,SAAS,OAAO,OAAO,CAAC,gBAAgB;IACxC,SAAS,OAAO,OAAO,CAAC,qBAAqB;IAC7C,SAAS,OAAO,OAAO,CAAC,cAAc;IACtC,SAAS,OAAO,OAAO,CAAC,mBAAmB;IAC3C,SAAS,OAAO,OAAO,CAAC,iBAAiB;IACzC,SAAS,OAAO,OAAO,CAAC,gBAAgB;IACxC,SAAS,OAAO,OAAO,CAAC,qBAAqB;IAC7C,SAAS,OAAO,OAAO,CAAC,qBAAqB;IAC7C,SAAS,OAAO,OAAO,CAAC,yBAAyB;IACjD,SAAS,OAAO,OAAO,CAAC,mBAAmB;IAC3C,SAAS,OAAO,OAAO,CAAC,qBAAqB;IAE7C,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3515, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TFE/build-rot/build-rot-frontend/src/components/VisualBuilder.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useCallback } from 'react';\nimport { Plus, Play, Save, Download, Upload } from 'lucide-react';\nimport BuilderCard from './BuilderCard';\nimport ConnectionLine from './ConnectionLine';\nimport CardLibrary from './CardLibrary';\nimport { generateCodeFromFlow } from '../lib/codeGenerator';\n\ninterface Connection {\n  id: string;\n  fromCardId: string;\n  fromOutput: string;\n  toCardId: string;\n  toInput: string;\n}\n\ninterface CardInstance {\n  id: string;\n  type: string;\n  position: { x: number; y: number };\n  data: Record<string, any>;\n}\n\ninterface Bot {\n  id: string;\n  name: string;\n  description: string;\n  discord_username: string;\n  status: 'active' | 'inactive';\n  created_at: string;\n}\n\ninterface VisualBuilderProps {\n  bot: Bot;\n  onBotUpdated: (bot: Bot) => void;\n}\n\nexport default function VisualBuilder({ bot, onBotUpdated }: VisualBuilderProps) {\n  const [cards, setCards] = useState<CardInstance[]>([]);\n  const [connections, setConnections] = useState<Connection[]>([]);\n  const [selectedCard, setSelectedCard] = useState<string | null>(null);\n  const [draggedCard, setDraggedCard] = useState<string | null>(null);\n  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });\n  const [showLibrary, setShowLibrary] = useState(false);\n  const [connecting, setConnecting] = useState<{\n    cardId: string;\n    output: string;\n    outputType: string;\n  } | null>(null);\n  const [variables, setVariables] = useState<Record<string, string>>({\n    BOT_ID: bot.id,\n    BOT_NAME: bot.name,\n    SERVER_URL: 'http://localhost:5001'\n  });\n  const [zoom, setZoom] = useState(1);\n  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 });\n  const [isPanning, setIsPanning] = useState(false);\n  const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 });\n\n  const canvasRef = useRef<HTMLDivElement>(null);\n\n  const addCard = useCallback((cardType: string, position: { x: number; y: number }) => {\n    const adjustedPosition = {\n      x: (position.x - panOffset.x) / zoom,\n      y: (position.y - panOffset.y) / zoom\n    };\n    \n    const newCard: CardInstance = {\n      id: `card_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      type: cardType,\n      position: adjustedPosition,\n      data: {}\n    };\n    setCards(prev => [...prev, newCard]);\n    setShowLibrary(false);\n  }, [zoom, panOffset]);\n\n  const updateCard = useCallback((cardId: string, data: Record<string, any>) => {\n    setCards(prev => prev.map(card => \n      card.id === cardId ? { ...card, data: { ...card.data, ...data } } : card\n    ));\n  }, []);\n\n  const deleteCard = useCallback((cardId: string) => {\n    setCards(prev => prev.filter(card => card.id !== cardId));\n    setConnections(prev => prev.filter(conn => \n      conn.fromCardId !== cardId && conn.toCardId !== cardId\n    ));\n  }, []);\n\n  const startConnection = useCallback((cardId: string, output: string, outputType: string) => {\n    setConnecting({ cardId, output, outputType });\n  }, []);\n\n  const completeConnection = useCallback((toCardId: string, toInput: string, inputType: string) => {\n    if (!connecting) return;\n    \n    if (connecting.outputType === inputType || inputType === 'any' || connecting.outputType === 'any') {\n      const newConnection: Connection = {\n        id: `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        fromCardId: connecting.cardId,\n        fromOutput: connecting.output,\n        toCardId,\n        toInput\n      };\n      \n      setConnections(prev => [\n        ...prev.filter(conn => !(conn.toCardId === toCardId && conn.toInput === toInput)),\n        newConnection\n      ]);\n    }\n    \n    setConnecting(null);\n  }, [connecting]);\n\n  const handleCardMouseDown = useCallback((e: React.MouseEvent, cardId: string) => {\n    if (e.button !== 0) return;\n    \n    const card = cards.find(c => c.id === cardId);\n    if (!card) return;\n\n    const rect = e.currentTarget.getBoundingClientRect();\n    setDragOffset({\n      x: e.clientX - rect.left,\n      y: e.clientY - rect.top\n    });\n    setDraggedCard(cardId);\n    setSelectedCard(cardId);\n  }, [cards]);\n\n  const handleMouseMove = useCallback((e: React.MouseEvent) => {\n    if (isPanning) {\n      const deltaX = e.clientX - lastPanPoint.x;\n      const deltaY = e.clientY - lastPanPoint.y;\n      \n      setPanOffset(prev => ({\n        x: prev.x + deltaX,\n        y: prev.y + deltaY\n      }));\n      \n      setLastPanPoint({ x: e.clientX, y: e.clientY });\n      return;\n    }\n\n    if (!draggedCard || !canvasRef.current) return;\n\n    const canvasRect = canvasRef.current.getBoundingClientRect();\n    const newPosition = {\n      x: (e.clientX - canvasRect.left - dragOffset.x - panOffset.x) / zoom,\n      y: (e.clientY - canvasRect.top - dragOffset.y - panOffset.y) / zoom\n    };\n\n    setCards(prev => prev.map(card =>\n      card.id === draggedCard ? { ...card, position: newPosition } : card\n    ));\n  }, [draggedCard, dragOffset, isPanning, lastPanPoint, zoom, panOffset]);\n\n  const handleMouseUp = useCallback(() => {\n    setDraggedCard(null);\n    setIsPanning(false);\n  }, []);\n\n  const handleCanvasMouseDown = useCallback((e: React.MouseEvent) => {\n    if (e.button === 1 || (e.button === 0 && e.altKey)) {\n      e.preventDefault();\n      setIsPanning(true);\n      setLastPanPoint({ x: e.clientX, y: e.clientY });\n    }\n  }, []);\n\n  const handleWheel = useCallback((e: React.WheelEvent) => {\n    e.preventDefault();\n    const delta = e.deltaY > 0 ? 0.9 : 1.1;\n    const newZoom = Math.max(0.1, Math.min(3, zoom * delta));\n    setZoom(newZoom);\n  }, [zoom]);\n\n  const resetView = useCallback(() => {\n    setZoom(1);\n    setPanOffset({ x: 0, y: 0 });\n  }, []);\n\n  const generateCode = useCallback(() => {\n    return generateCodeFromFlow(cards, connections, variables);\n  }, [cards, connections, variables]);\n\n  const saveFlow = useCallback(async () => {\n    const flowData = {\n      cards,\n      connections,\n      variables\n    };\n\n    try {\n      const token = localStorage.getItem('token');\n      await fetch(`http://localhost:5001/api/code/${bot.id}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify({ \n          code: generateCode(),\n          language: 'javascript',\n          flowData: JSON.stringify(flowData)\n        }),\n      });\n      console.log('Flow saved successfully');\n    } catch (error) {\n      console.error('Failed to save flow:', error);\n    }\n  }, [cards, connections, variables, bot.id, generateCode]);\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      <div className=\"flex items-center justify-between p-4 border-b bg-white\">\n        <div className=\"flex items-center space-x-4\">\n          <h3 className=\"text-lg font-medium\">Visual Bot Builder</h3>\n          <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n            <span>Zoom: {Math.round(zoom * 100)}%</span>\n            <button\n              onClick={resetView}\n              className=\"px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded\"\n            >\n              Reset View\n            </button>\n          </div>\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={() => setShowLibrary(true)}\n            className=\"flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\n          >\n            <Plus className=\"h-4 w-4 mr-1\" />\n            Add Card\n          </button>\n          <button\n            onClick={saveFlow}\n            className=\"flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700\"\n          >\n            <Save className=\"h-4 w-4 mr-1\" />\n            Save\n          </button>\n          <button\n            onClick={() => {\n              const code = generateCode();\n              navigator.clipboard.writeText(code);\n            }}\n            className=\"flex items-center px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700\"\n          >\n            <Download className=\"h-4 w-4 mr-1\" />\n            Export Code\n          </button>\n        </div>\n      </div>\n\n      <div className=\"flex-1 flex\">\n        <div className=\"flex-1 relative overflow-hidden\">\n          <div\n            ref={canvasRef}\n            className=\"w-full h-full bg-gray-50 relative cursor-grab active:cursor-grabbing\"\n            onMouseMove={handleMouseMove}\n            onMouseUp={handleMouseUp}\n            onMouseDown={handleCanvasMouseDown}\n            onWheel={handleWheel}\n            onClick={() => {\n              if (!connecting) {\n                setSelectedCard(null);\n              }\n            }}\n            style={{ cursor: isPanning ? 'grabbing' : 'grab' }}\n          >\n            <div \n              className=\"absolute inset-0 opacity-20\"\n              style={{\n                backgroundImage: `\n                  linear-gradient(to right, #e5e7eb 1px, transparent 1px),\n                  linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)\n                `,\n                backgroundSize: `${20 * zoom}px ${20 * zoom}px`,\n                backgroundPosition: `${panOffset.x}px ${panOffset.y}px`,\n                transform: `scale(${zoom})`,\n                transformOrigin: '0 0'\n              }}\n            />\n\n            <div\n              className=\"absolute inset-0\"\n              style={{\n                transform: `translate(${panOffset.x}px, ${panOffset.y}px) scale(${zoom})`,\n                transformOrigin: '0 0'\n              }}\n            >\n              {connections.map(connection => (\n                <ConnectionLine\n                  key={connection.id}\n                  connection={connection}\n                  cards={cards}\n                />\n              ))}\n\n              {cards.map(card => (\n                <BuilderCard\n                  key={card.id}\n                  card={card}\n                  isSelected={selectedCard === card.id}\n                  onMouseDown={(e) => handleCardMouseDown(e, card.id)}\n                  onUpdate={(data) => updateCard(card.id, data)}\n                  onDelete={() => deleteCard(card.id)}\n                  onStartConnection={startConnection}\n                  onCompleteConnection={completeConnection}\n                  connecting={connecting}\n                  variables={variables}\n                />\n              ))}\n            </div>\n          </div>\n        </div>\n\n        <div className=\"w-80 bg-white border-l p-4 overflow-y-auto\">\n          <div className=\"mb-4\">\n            <h4 className=\"font-medium mb-2\">Controls</h4>\n            <div className=\"text-xs text-gray-600 space-y-1\">\n              <div>• Mouse wheel: Zoom in/out</div>\n              <div>• Alt + Click: Pan canvas</div>\n              <div>• Middle click: Pan canvas</div>\n            </div>\n          </div>\n\n          <h4 className=\"font-medium mb-3\">Variables</h4>\n          <div className=\"space-y-2\">\n            {Object.entries(variables).map(([key, value]) => (\n              <div key={key} className=\"flex items-center space-x-2\">\n                <span className=\"text-sm font-mono text-gray-600 w-20\">{key}:</span>\n                <input\n                  type=\"text\"\n                  value={value}\n                  onChange={(e) => setVariables(prev => ({ ...prev, [key]: e.target.value }))}\n                  className=\"flex-1 px-2 py-1 text-sm border rounded\"\n                />\n              </div>\n            ))}\n            <button\n              onClick={() => {\n                const newKey = prompt('Variable name:');\n                if (newKey && !variables[newKey]) {\n                  setVariables(prev => ({ ...prev, [newKey]: '' }));\n                }\n              }}\n              className=\"text-sm text-blue-600 hover:text-blue-800\"\n            >\n              + Add Variable\n            </button>\n          </div>\n\n          {selectedCard && (\n            <div className=\"mt-6\">\n              <h4 className=\"font-medium mb-3\">Card Properties</h4>\n              <div className=\"text-sm text-gray-600\">\n                Selected: {cards.find(c => c.id === selectedCard)?.type}\n              </div>\n            </div>\n          )}\n\n          <div className=\"mt-6\">\n            <h4 className=\"font-medium mb-3\">Available Variables</h4>\n            <div className=\"text-xs text-gray-600 space-y-1\">\n              <div><code>{'{{BOT_ID}}'}</code> - Bot ID</div>\n              <div><code>{'{{BOT_NAME}}'}</code> - Bot Name</div>\n              <div><code>{'{{author}}'}</code> - Message author</div>\n              <div><code>{'{{content}}'}</code> - Message content</div>\n              <div><code>{'{{channel}}'}</code> - Channel name</div>\n              <div><code>{'{{member.username}}'}</code> - Member username</div>\n              <div><code>{'{{member.id}}'}</code> - Member ID</div>\n              <div><code>{'{{guild.name}}'}</code> - Server name</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {showLibrary && (\n        <CardLibrary\n          onAddCard={addCard}\n          onClose={() => setShowLibrary(false)}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAsCe,SAAS,cAAc,EAAE,GAAG,EAAE,YAAY,EAAsB;;IAC7E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAIjC;IACV,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;QACjE,QAAQ,IAAI,EAAE;QACd,UAAU,IAAI,IAAI;QAClB,YAAY;IACd;IACA,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACxD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAE9D,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEzC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,CAAC,UAAkB;YAC7C,MAAM,mBAAmB;gBACvB,GAAG,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC,IAAI;gBAChC,GAAG,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC,IAAI;YAClC;YAEA,MAAM,UAAwB;gBAC5B,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;gBACnE,MAAM;gBACN,UAAU;gBACV,MAAM,CAAC;YACT;YACA;sDAAS,CAAA,OAAQ;2BAAI;wBAAM;qBAAQ;;YACnC,eAAe;QACjB;6CAAG;QAAC;QAAM;KAAU;IAEpB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC,QAAgB;YAC9C;yDAAS,CAAA,OAAQ,KAAK,GAAG;iEAAC,CAAA,OACxB,KAAK,EAAE,KAAK,SAAS;gCAAE,GAAG,IAAI;gCAAE,MAAM;oCAAE,GAAG,KAAK,IAAI;oCAAE,GAAG,IAAI;gCAAC;4BAAE,IAAI;;;QAExE;gDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC;YAC9B;yDAAS,CAAA,OAAQ,KAAK,MAAM;iEAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;;;YACjD;yDAAe,CAAA,OAAQ,KAAK,MAAM;iEAAC,CAAA,OACjC,KAAK,UAAU,KAAK,UAAU,KAAK,QAAQ,KAAK;;;QAEpD;gDAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,QAAgB,QAAgB;YACnE,cAAc;gBAAE;gBAAQ;gBAAQ;YAAW;QAC7C;qDAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC,UAAkB,SAAiB;YACzE,IAAI,CAAC,YAAY;YAEjB,IAAI,WAAW,UAAU,KAAK,aAAa,cAAc,SAAS,WAAW,UAAU,KAAK,OAAO;gBACjG,MAAM,gBAA4B;oBAChC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;oBACnE,YAAY,WAAW,MAAM;oBAC7B,YAAY,WAAW,MAAM;oBAC7B;oBACA;gBACF;gBAEA;qEAAe,CAAA,OAAQ;+BAClB,KAAK,MAAM;iFAAC,CAAA,OAAQ,CAAC,CAAC,KAAK,QAAQ,KAAK,YAAY,KAAK,OAAO,KAAK,OAAO;;4BAC/E;yBACD;;YACH;YAEA,cAAc;QAChB;wDAAG;QAAC;KAAW;IAEf,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC,GAAqB;YAC5D,IAAI,EAAE,MAAM,KAAK,GAAG;YAEpB,MAAM,OAAO,MAAM,IAAI;uEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;YACtC,IAAI,CAAC,MAAM;YAEX,MAAM,OAAO,EAAE,aAAa,CAAC,qBAAqB;YAClD,cAAc;gBACZ,GAAG,EAAE,OAAO,GAAG,KAAK,IAAI;gBACxB,GAAG,EAAE,OAAO,GAAG,KAAK,GAAG;YACzB;YACA,eAAe;YACf,gBAAgB;QAClB;yDAAG;QAAC;KAAM;IAEV,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YACnC,IAAI,WAAW;gBACb,MAAM,SAAS,EAAE,OAAO,GAAG,aAAa,CAAC;gBACzC,MAAM,SAAS,EAAE,OAAO,GAAG,aAAa,CAAC;gBAEzC;kEAAa,CAAA,OAAQ,CAAC;4BACpB,GAAG,KAAK,CAAC,GAAG;4BACZ,GAAG,KAAK,CAAC,GAAG;wBACd,CAAC;;gBAED,gBAAgB;oBAAE,GAAG,EAAE,OAAO;oBAAE,GAAG,EAAE,OAAO;gBAAC;gBAC7C;YACF;YAEA,IAAI,CAAC,eAAe,CAAC,UAAU,OAAO,EAAE;YAExC,MAAM,aAAa,UAAU,OAAO,CAAC,qBAAqB;YAC1D,MAAM,cAAc;gBAClB,GAAG,CAAC,EAAE,OAAO,GAAG,WAAW,IAAI,GAAG,WAAW,CAAC,GAAG,UAAU,CAAC,IAAI;gBAChE,GAAG,CAAC,EAAE,OAAO,GAAG,WAAW,GAAG,GAAG,WAAW,CAAC,GAAG,UAAU,CAAC,IAAI;YACjE;YAEA;8DAAS,CAAA,OAAQ,KAAK,GAAG;sEAAC,CAAA,OACxB,KAAK,EAAE,KAAK,cAAc;gCAAE,GAAG,IAAI;gCAAE,UAAU;4BAAY,IAAI;;;QAEnE;qDAAG;QAAC;QAAa;QAAY;QAAW;QAAc;QAAM;KAAU;IAEtE,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YAChC,eAAe;YACf,aAAa;QACf;mDAAG,EAAE;IAEL,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAC;YACzC,IAAI,EAAE,MAAM,KAAK,KAAM,EAAE,MAAM,KAAK,KAAK,EAAE,MAAM,EAAG;gBAClD,EAAE,cAAc;gBAChB,aAAa;gBACb,gBAAgB;oBAAE,GAAG,EAAE,OAAO;oBAAE,GAAG,EAAE,OAAO;gBAAC;YAC/C;QACF;2DAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YAC/B,EAAE,cAAc;YAChB,MAAM,QAAQ,EAAE,MAAM,GAAG,IAAI,MAAM;YACnC,MAAM,UAAU,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,OAAO;YACjD,QAAQ;QACV;iDAAG;QAAC;KAAK;IAET,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YAC5B,QAAQ;YACR,aAAa;gBAAE,GAAG;gBAAG,GAAG;YAAE;QAC5B;+CAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YAC/B,OAAO,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO,aAAa;QAClD;kDAAG;QAAC;QAAO;QAAa;KAAU;IAElC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YAC3B,MAAM,WAAW;gBACf;gBACA;gBACA;YACF;YAEA,IAAI;gBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,MAAM,MAAM,CAAC,+BAA+B,EAAE,IAAI,EAAE,EAAE,EAAE;oBACtD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBACpC;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,MAAM;wBACN,UAAU;wBACV,UAAU,KAAK,SAAS,CAAC;oBAC3B;gBACF;gBACA,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;YACxC;QACF;8CAAG;QAAC;QAAO;QAAa;QAAW,IAAI,EAAE;QAAE;KAAa;IAExD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsB;;;;;;0CACpC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAK;4CAAO,KAAK,KAAK,CAAC,OAAO;4CAAK;;;;;;;kDACpC,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAKL,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,6LAAC;gCACC,SAAS;oCACP,MAAM,OAAO;oCACb,UAAU,SAAS,CAAC,SAAS,CAAC;gCAChC;gCACA,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAM3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,KAAK;4BACL,WAAU;4BACV,aAAa;4BACb,WAAW;4BACX,aAAa;4BACb,SAAS;4BACT,SAAS;gCACP,IAAI,CAAC,YAAY;oCACf,gBAAgB;gCAClB;4BACF;4BACA,OAAO;gCAAE,QAAQ,YAAY,aAAa;4BAAO;;8CAEjD,6LAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAiB,CAAC;;;gBAGlB,CAAC;wCACD,gBAAgB,GAAG,KAAK,KAAK,GAAG,EAAE,KAAK,KAAK,EAAE,CAAC;wCAC/C,oBAAoB,GAAG,UAAU,CAAC,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC;wCACvD,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;wCAC3B,iBAAiB;oCACnB;;;;;;8CAGF,6LAAC;oCACC,WAAU;oCACV,OAAO;wCACL,WAAW,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;wCACzE,iBAAiB;oCACnB;;wCAEC,YAAY,GAAG,CAAC,CAAA,2BACf,6LAAC,uIAAA,CAAA,UAAc;gDAEb,YAAY;gDACZ,OAAO;+CAFF,WAAW,EAAE;;;;;wCAMrB,MAAM,GAAG,CAAC,CAAA,qBACT,6LAAC,oIAAA,CAAA,UAAW;gDAEV,MAAM;gDACN,YAAY,iBAAiB,KAAK,EAAE;gDACpC,aAAa,CAAC,IAAM,oBAAoB,GAAG,KAAK,EAAE;gDAClD,UAAU,CAAC,OAAS,WAAW,KAAK,EAAE,EAAE;gDACxC,UAAU,IAAM,WAAW,KAAK,EAAE;gDAClC,mBAAmB;gDACnB,sBAAsB;gDACtB,YAAY;gDACZ,WAAW;+CATN,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;kCAgBtB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAI;;;;;;0DACL,6LAAC;0DAAI;;;;;;0DACL,6LAAC;0DAAI;;;;;;;;;;;;;;;;;;0CAIT,6LAAC;gCAAG,WAAU;0CAAmB;;;;;;0CACjC,6LAAC;gCAAI,WAAU;;oCACZ,OAAO,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC1C,6LAAC;4CAAc,WAAU;;8DACvB,6LAAC;oDAAK,WAAU;;wDAAwC;wDAAI;;;;;;;8DAC5D,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,aAAa,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDACzE,WAAU;;;;;;;2CANJ;;;;;kDAUZ,6LAAC;wCACC,SAAS;4CACP,MAAM,SAAS,OAAO;4CACtB,IAAI,UAAU,CAAC,SAAS,CAAC,OAAO,EAAE;gDAChC,aAAa,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,CAAC,OAAO,EAAE;oDAAG,CAAC;4CACjD;wCACF;wCACA,WAAU;kDACX;;;;;;;;;;;;4BAKF,8BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,6LAAC;wCAAI,WAAU;;4CAAwB;4CAC1B,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe;;;;;;;;;;;;;0CAKzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEAAI,6LAAC;kEAAM;;;;;;oDAAoB;;;;;;;0DAChC,6LAAC;;kEAAI,6LAAC;kEAAM;;;;;;oDAAsB;;;;;;;0DAClC,6LAAC;;kEAAI,6LAAC;kEAAM;;;;;;oDAAoB;;;;;;;0DAChC,6LAAC;;kEAAI,6LAAC;kEAAM;;;;;;oDAAqB;;;;;;;0DACjC,6LAAC;;kEAAI,6LAAC;kEAAM;;;;;;oDAAqB;;;;;;;0DACjC,6LAAC;;kEAAI,6LAAC;kEAAM;;;;;;oDAA6B;;;;;;;0DACzC,6LAAC;;kEAAI,6LAAC;kEAAM;;;;;;oDAAuB;;;;;;;0DACnC,6LAAC;;kEAAI,6LAAC;kEAAM;;;;;;oDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAM3C,6BACC,6LAAC,oIAAA,CAAA,UAAW;gBACV,WAAW;gBACX,SAAS,IAAM,eAAe;;;;;;;;;;;;AAKxC;GA/VwB;KAAA", "debugId": null}}, {"offset": {"line": 4336, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TFE/build-rot/build-rot-frontend/src/components/Dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Plus, Bot, Play, Square, Settings, Code, LogOut, Blocks } from 'lucide-react';\nimport BotCard from './BotCard';\nimport AddBotModal from './AddBotModal';\nimport CodeEditor from './CodeEditor';\nimport VisualBuilder from './VisualBuilder';\n\ninterface User {\n  id: string;\n  email: string;\n}\n\ninterface Bot {\n  id: string;\n  name: string;\n  description: string;\n  discord_username: string;\n  status: 'active' | 'inactive';\n  created_at: string;\n}\n\ninterface DashboardProps {\n  user: User;\n  onLogout: () => void;\n}\n\nexport default function Dashboard({ user, onLogout }: DashboardProps) {\n  const [bots, setBots] = useState<Bot[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [showAddBot, setShowAddBot] = useState(false);\n  const [selectedBot, setSelectedBot] = useState<Bot | null>(null);\n  const [activeTab, setActiveTab] = useState<'overview' | 'code' | 'visual'>('overview');\n\n  useEffect(() => {\n    fetchBots();\n  }, []);\n\n  const fetchBots = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch('http://localhost:5001/api/bots', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setBots(data.bots);\n      }\n    } catch (error) {\n      console.error('Failed to fetch bots:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('token');\n    onLogout();\n  };\n\n  const handleBotAdded = (newBot: Bot) => {\n    setBots([...bots, newBot]);\n    setShowAddBot(false);\n  };\n\n  const handleBotUpdated = (updatedBot: Bot) => {\n    setBots(bots.map(bot => bot.id === updatedBot.id ? updatedBot : bot));\n    if (selectedBot?.id === updatedBot.id) {\n      setSelectedBot(updatedBot);\n    }\n  };\n\n  const handleBotDeleted = (botId: string) => {\n    setBots(bots.filter(bot => bot.id !== botId));\n    if (selectedBot?.id === botId) {\n      setSelectedBot(null);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Bot className=\"h-8 w-8 text-blue-600 mr-3\" />\n              <h1 className=\"text-xl font-semibold text-gray-900\">Build Rot</h1>\n            </div>\n            \n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-600\">Welcome, {user.email}</span>\n              <button\n                onClick={handleLogout}\n                className=\"flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900\"\n              >\n                <LogOut className=\"h-4 w-4 mr-1\" />\n                Logout\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {selectedBot ? (\n          <div className=\"space-y-6\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => setSelectedBot(null)}\n                  className=\"text-blue-600 hover:text-blue-800\"\n                >\n                  ← Back to Dashboard\n                </button>\n                <h2 className=\"text-2xl font-bold text-gray-900\">{selectedBot.name}</h2>\n                <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                  selectedBot.status === 'active' \n                    ? 'bg-green-100 text-green-800' \n                    : 'bg-gray-100 text-gray-800'\n                }`}>\n                  {selectedBot.status}\n                </span>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow\">\n              <div className=\"border-b border-gray-200\">\n                <nav className=\"-mb-px flex\">\n                  <button\n                    onClick={() => setActiveTab('overview')}\n                    className={`py-2 px-4 border-b-2 font-medium text-sm ${\n                      activeTab === 'overview'\n                        ? 'border-blue-500 text-blue-600'\n                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                    }`}\n                  >\n                    Overview\n                  </button>\n                  <button\n                    onClick={() => setActiveTab('visual')}\n                    className={`py-2 px-4 border-b-2 font-medium text-sm ${\n                      activeTab === 'visual'\n                        ? 'border-blue-500 text-blue-600'\n                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                    }`}\n                  >\n                    <Blocks className=\"h-4 w-4 inline mr-1\" />\n                    Visual Builder\n                  </button>\n                  <button\n                    onClick={() => setActiveTab('code')}\n                    className={`py-2 px-4 border-b-2 font-medium text-sm ${\n                      activeTab === 'code'\n                        ? 'border-blue-500 text-blue-600'\n                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                    }`}\n                  >\n                    <Code className=\"h-4 w-4 inline mr-1\" />\n                    Code Editor\n                  </button>\n                </nav>\n              </div>\n\n              <div className=\"p-6\">\n                {activeTab === 'overview' ? (\n                  <div className=\"space-y-6\">\n                    <div>\n                      <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Bot Information</h3>\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700\">Name</label>\n                          <p className=\"mt-1 text-sm text-gray-900\">{selectedBot.name}</p>\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700\">Discord Username</label>\n                          <p className=\"mt-1 text-sm text-gray-900\">{selectedBot.discord_username}</p>\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700\">Status</label>\n                          <p className=\"mt-1 text-sm text-gray-900 capitalize\">{selectedBot.status}</p>\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700\">Created</label>\n                          <p className=\"mt-1 text-sm text-gray-900\">\n                            {new Date(selectedBot.created_at).toLocaleDateString()}\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n\n                    {selectedBot.description && (\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700\">Description</label>\n                        <p className=\"mt-1 text-sm text-gray-900\">{selectedBot.description}</p>\n                      </div>\n                    )}\n                  </div>\n                ) : activeTab === 'visual' ? (\n                  <VisualBuilder\n                    bot={selectedBot}\n                    onBotUpdated={handleBotUpdated}\n                  />\n                ) : (\n                  <CodeEditor\n                    bot={selectedBot}\n                    onBotUpdated={handleBotUpdated}\n                  />\n                )}\n              </div>\n            </div>\n          </div>\n        ) : (\n          <div className=\"space-y-6\">\n            <div className=\"flex justify-between items-center\">\n              <h2 className=\"text-2xl font-bold text-gray-900\">Your Discord Bots</h2>\n              <button\n                onClick={() => setShowAddBot(true)}\n                className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n              >\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Add Bot\n              </button>\n            </div>\n\n            {bots.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <Bot className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No bots yet</h3>\n                <p className=\"text-gray-600 mb-4\">Get started by adding your first Discord bot</p>\n                <button\n                  onClick={() => setShowAddBot(true)}\n                  className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n                >\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Add Your First Bot\n                </button>\n              </div>\n            ) : (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {bots.map((bot) => (\n                  <BotCard\n                    key={bot.id}\n                    bot={bot}\n                    onSelect={() => setSelectedBot(bot)}\n                    onUpdate={handleBotUpdated}\n                    onDelete={handleBotDeleted}\n                  />\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n      </main>\n\n      {showAddBot && (\n        <AddBotModal\n          onClose={() => setShowAddBot(false)}\n          onBotAdded={handleBotAdded}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AA4Be,SAAS,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAkB;;IAClE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC;IAE3E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,kCAAkC;gBAC7D,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,QAAQ,KAAK,IAAI;YACnB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,QAAQ;eAAI;YAAM;SAAO;QACzB,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,WAAW,EAAE,GAAG,aAAa;QAChE,IAAI,aAAa,OAAO,WAAW,EAAE,EAAE;YACrC,eAAe;QACjB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QACtC,IAAI,aAAa,OAAO,OAAO;YAC7B,eAAe;QACjB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAGtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CAAwB;4CAAU,KAAK,KAAK;;;;;;;kDAC5D,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7C,6LAAC;gBAAK,WAAU;0BACb,4BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCAAG,WAAU;kDAAoC,YAAY,IAAI;;;;;;kDAClE,6LAAC;wCAAK,WAAW,CAAC,2CAA2C,EAC3D,YAAY,MAAM,KAAK,WACnB,gCACA,6BACJ;kDACC,YAAY,MAAM;;;;;;;;;;;;;;;;;sCAKzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,aACV,kCACA,8EACJ;0DACH;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,WACV,kCACA,8EACJ;;kEAEF,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAG5C,6LAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,SACV,kCACA,8EACJ;;kEAEF,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;;;;;;;;;;;;8CAM9C,6LAAC;oCAAI,WAAU;8CACZ,cAAc,2BACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAA0C;;;;;;kFAC3D,6LAAC;wEAAE,WAAU;kFAA8B,YAAY,IAAI;;;;;;;;;;;;0EAE7D,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAA0C;;;;;;kFAC3D,6LAAC;wEAAE,WAAU;kFAA8B,YAAY,gBAAgB;;;;;;;;;;;;0EAEzE,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAA0C;;;;;;kFAC3D,6LAAC;wEAAE,WAAU;kFAAyC,YAAY,MAAM;;;;;;;;;;;;0EAE1E,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAA0C;;;;;;kFAC3D,6LAAC;wEAAE,WAAU;kFACV,IAAI,KAAK,YAAY,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;4CAM3D,YAAY,WAAW,kBACtB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA0C;;;;;;kEAC3D,6LAAC;wDAAE,WAAU;kEAA8B,YAAY,WAAW;;;;;;;;;;;;;;;;;+CAItE,cAAc,yBAChB,6LAAC,sIAAA,CAAA,UAAa;wCACZ,KAAK;wCACL,cAAc;;;;;6DAGhB,6LAAC,mIAAA,CAAA,UAAU;wCACT,KAAK;wCACL,cAAc;;;;;;;;;;;;;;;;;;;;;;yCAOxB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCACC,SAAS,IAAM,cAAc;oCAC7B,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;wBAKpC,KAAK,MAAM,KAAK,kBACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC;oCACC,SAAS,IAAM,cAAc;oCAC7B,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;iDAKrC,6LAAC;4BAAI,WAAU;sCACZ,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC,gIAAA,CAAA,UAAO;oCAEN,KAAK;oCACL,UAAU,IAAM,eAAe;oCAC/B,UAAU;oCACV,UAAU;mCAJL,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;YAaxB,4BACC,6LAAC,oIAAA,CAAA,UAAW;gBACV,SAAS,IAAM,cAAc;gBAC7B,YAAY;;;;;;;;;;;;AAKtB;GAtPwB;KAAA", "debugId": null}}, {"offset": {"line": 4955, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TFE/build-rot/build-rot-frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport AuthForm from '@/components/AuthForm';\nimport Dashboard from '@/components/Dashboard';\n\nexport default function Home() {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      fetch('http://localhost:5001/api/auth/me', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      })\n      .then(res => res.json())\n      .then(data => {\n        if (data.user) {\n          setUser(data.user);\n        } else {\n          localStorage.removeItem('token');\n        }\n      })\n      .catch(() => {\n        localStorage.removeItem('token');\n      })\n      .finally(() => {\n        setLoading(false);\n      });\n    } else {\n      setLoading(false);\n    }\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {user ? (\n        <Dashboard user={user} onLogout={() => setUser(null)} />\n      ) : (\n        <AuthForm onLogin={setUser} />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,OAAO;gBACT,MAAM,qCAAqC;oBACzC,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBACpC;gBACF,GACC,IAAI;sCAAC,CAAA,MAAO,IAAI,IAAI;qCACpB,IAAI;sCAAC,CAAA;wBACJ,IAAI,KAAK,IAAI,EAAE;4BACb,QAAQ,KAAK,IAAI;wBACnB,OAAO;4BACL,aAAa,UAAU,CAAC;wBAC1B;oBACF;qCACC,KAAK;sCAAC;wBACL,aAAa,UAAU,CAAC;oBAC1B;qCACC,OAAO;sCAAC;wBACP,WAAW;oBACb;;YACF,OAAO;gBACL,WAAW;YACb;QACF;yBAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,qBACC,6LAAC,kIAAA,CAAA,UAAS;YAAC,MAAM;YAAM,UAAU,IAAM,QAAQ;;;;;iCAE/C,6LAAC,iIAAA,CAAA,UAAQ;YAAC,SAAS;;;;;;;;;;;AAI3B;GAhDwB;KAAA", "debugId": null}}]}