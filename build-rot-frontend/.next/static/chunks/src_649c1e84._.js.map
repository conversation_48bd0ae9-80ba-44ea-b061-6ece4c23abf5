{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TFE/build-rot/build-rot-frontend/src/components/AuthForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface AuthFormProps {\n  onLogin: (user: any) => void;\n}\n\nexport default function AuthForm({ onLogin }: AuthFormProps) {\n  const [isLogin, setIsLogin] = useState(true);\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const endpoint = isLogin ? '/api/auth/login' : '/api/auth/register';\n      const response = await fetch(`http://localhost:5001${endpoint}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, password }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        if (isLogin) {\n          localStorage.setItem('token', data.token);\n          onLogin(data.user);\n        } else {\n          setIsLogin(true);\n          setError('Registration successful! Please login.');\n        }\n      } else {\n        setError(data.error || 'Something went wrong');\n      }\n    } catch (err) {\n      setError('Network error. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"max-w-md w-full space-y-8 p-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">Build Rot</h1>\n          <p className=\"text-gray-600\">Discord Bot Builder Platform</p>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-lg p-8\">\n          <div className=\"text-center mb-6\">\n            <h2 className=\"text-2xl font-semibold text-gray-900\">\n              {isLogin ? 'Sign In' : 'Create Account'}\n            </h2>\n            <p className=\"text-gray-600 mt-2\">\n              {isLogin ? 'Welcome back!' : 'Join the bot building community'}\n            </p>\n          </div>\n\n          {error && (\n            <div className=\"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\">\n              {error}\n            </div>\n          )}\n\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email Address\n              </label>\n              <input\n                id=\"email\"\n                type=\"email\"\n                required\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Enter your email\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                type=\"password\"\n                required\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Enter your password\"\n                minLength={6}\n              />\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? (\n                <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"></div>\n              ) : (\n                isLogin ? 'Sign In' : 'Create Account'\n              )}\n            </button>\n          </form>\n\n          <div className=\"mt-6 text-center\">\n            <button\n              onClick={() => {\n                setIsLogin(!isLogin);\n                setError('');\n              }}\n              className=\"text-blue-600 hover:text-blue-500 text-sm\"\n            >\n              {isLogin ? \"Don't have an account? Sign up\" : \"Already have an account? Sign in\"}\n            </button>\n          </div>\n        </div>\n\n        <div className=\"text-center text-sm text-gray-500\">\n          <p>Build powerful Discord bots with ease</p>\n          <p>No coding experience required!</p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAQe,SAAS,SAAS,EAAE,OAAO,EAAiB;;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,UAAU,oBAAoB;YAC/C,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,UAAU,EAAE;gBAC/D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAS;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,IAAI,SAAS;oBACX,aAAa,OAAO,CAAC,SAAS,KAAK,KAAK;oBACxC,QAAQ,KAAK,IAAI;gBACnB,OAAO;oBACL,WAAW;oBACX,SAAS;gBACX;YACF,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAG/B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,UAAU,YAAY;;;;;;8CAEzB,6LAAC;oCAAE,WAAU;8CACV,UAAU,kBAAkB;;;;;;;;;;;;wBAIhC,uBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,QAAQ;4CACR,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,QAAQ;4CACR,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,WAAU;4CACV,aAAY;4CACZ,WAAW;;;;;;;;;;;;8CAIf,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,wBACC,6LAAC;wCAAI,WAAU;;;;;+CAEf,UAAU,YAAY;;;;;;;;;;;;sCAK5B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;oCACP,WAAW,CAAC;oCACZ,SAAS;gCACX;gCACA,WAAU;0CAET,UAAU,mCAAmC;;;;;;;;;;;;;;;;;8BAKpD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAE;;;;;;sCACH,6LAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAKb;GAnIwB;KAAA", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TFE/build-rot/build-rot-frontend/src/components/BotCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Bot, Play, Square, Settings, Trash2, MoreVertical } from 'lucide-react';\n\ninterface Bot {\n  id: string;\n  name: string;\n  description: string;\n  discord_username: string;\n  status: 'active' | 'inactive';\n  created_at: string;\n}\n\ninterface BotCardProps {\n  bot: Bot;\n  onSelect: () => void;\n  onUpdate: (bot: Bot) => void;\n  onDelete: (botId: string) => void;\n}\n\nexport default function BotCard({ bot, onSelect, onUpdate, onDelete }: BotCardProps) {\n  const [loading, setLoading] = useState(false);\n  const [showMenu, setShowMenu] = useState(false);\n\n  const handleStatusToggle = async (e: React.MouseEvent) => {\n    e.stopPropagation();\n    setLoading(true);\n\n    try {\n      const token = localStorage.getItem('token');\n      const action = bot.status === 'active' ? 'stop' : 'start';\n      \n      const response = await fetch(`http://localhost:5001/api/bots/${bot.id}/${action}`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        onUpdate({\n          ...bot,\n          status: bot.status === 'active' ? 'inactive' : 'active'\n        });\n      }\n    } catch (error) {\n      console.error('Failed to toggle bot status:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDelete = async (e: React.MouseEvent) => {\n    e.stopPropagation();\n    \n    if (!confirm('Are you sure you want to delete this bot? This action cannot be undone.')) {\n      return;\n    }\n\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch(`http://localhost:5001/api/bots/${bot.id}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        onDelete(bot.id);\n      }\n    } catch (error) {\n      console.error('Failed to delete bot:', error);\n    }\n  };\n\n  return (\n    <div \n      onClick={onSelect}\n      className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer relative\"\n    >\n      <div className=\"flex items-start justify-between mb-4\">\n        <div className=\"flex items-center\">\n          <div className=\"p-2 bg-blue-100 rounded-lg mr-3\">\n            <Bot className=\"h-6 w-6 text-blue-600\" />\n          </div>\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">{bot.name}</h3>\n            <p className=\"text-sm text-gray-600\">@{bot.discord_username}</p>\n          </div>\n        </div>\n        \n        <div className=\"relative\">\n          <button\n            onClick={(e) => {\n              e.stopPropagation();\n              setShowMenu(!showMenu);\n            }}\n            className=\"p-1 text-gray-400 hover:text-gray-600\"\n          >\n            <MoreVertical className=\"h-4 w-4\" />\n          </button>\n          \n          {showMenu && (\n            <div className=\"absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10\">\n              <button\n                onClick={handleDelete}\n                className=\"w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center\"\n              >\n                <Trash2 className=\"h-4 w-4 mr-2\" />\n                Delete Bot\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {bot.description && (\n        <p className=\"text-sm text-gray-600 mb-4 line-clamp-2\">{bot.description}</p>\n      )}\n\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center\">\n          <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n            bot.status === 'active' \n              ? 'bg-green-100 text-green-800' \n              : 'bg-gray-100 text-gray-800'\n          }`}>\n            {bot.status}\n          </span>\n        </div>\n\n        <button\n          onClick={handleStatusToggle}\n          disabled={loading}\n          className={`flex items-center px-3 py-1 rounded-md text-sm font-medium transition-colors ${\n            bot.status === 'active'\n              ? 'bg-red-100 text-red-700 hover:bg-red-200'\n              : 'bg-green-100 text-green-700 hover:bg-green-200'\n          } disabled:opacity-50`}\n        >\n          {loading ? (\n            <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-1\"></div>\n          ) : bot.status === 'active' ? (\n            <Square className=\"h-4 w-4 mr-1\" />\n          ) : (\n            <Play className=\"h-4 w-4 mr-1\" />\n          )}\n          {bot.status === 'active' ? 'Stop' : 'Start'}\n        </button>\n      </div>\n\n      <div className=\"mt-4 pt-4 border-t border-gray-100\">\n        <p className=\"text-xs text-gray-500\">\n          Created {new Date(bot.created_at).toLocaleDateString()}\n        </p>\n      </div>\n\n      {showMenu && (\n        <div \n          className=\"fixed inset-0 z-0\" \n          onClick={(e) => {\n            e.stopPropagation();\n            setShowMenu(false);\n          }}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAqBe,SAAS,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAgB;;IACjF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,qBAAqB,OAAO;QAChC,EAAE,eAAe;QACjB,WAAW;QAEX,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,SAAS,IAAI,MAAM,KAAK,WAAW,SAAS;YAElD,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE;gBACjF,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS;oBACP,GAAG,GAAG;oBACN,QAAQ,IAAI,MAAM,KAAK,WAAW,aAAa;gBACjD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,eAAe;QAEjB,IAAI,CAAC,QAAQ,4EAA4E;YACvF;QACF;QAEA,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,IAAI,EAAE,EAAE,EAAE;gBACvE,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS,IAAI,EAAE;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAEjB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAuC,IAAI,IAAI;;;;;;kDAC7D,6LAAC;wCAAE,WAAU;;4CAAwB;4CAAE,IAAI,gBAAgB;;;;;;;;;;;;;;;;;;;kCAI/D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB,YAAY,CAAC;gCACf;gCACA,WAAU;0CAEV,cAAA,6LAAC,6NAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;4BAGzB,0BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;YAQ5C,IAAI,WAAW,kBACd,6LAAC;gBAAE,WAAU;0BAA2C,IAAI,WAAW;;;;;;0BAGzE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAW,CAAC,2CAA2C,EAC3D,IAAI,MAAM,KAAK,WACX,gCACA,6BACJ;sCACC,IAAI,MAAM;;;;;;;;;;;kCAIf,6LAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAW,CAAC,6EAA6E,EACvF,IAAI,MAAM,KAAK,WACX,6CACA,iDACL,oBAAoB,CAAC;;4BAErB,wBACC,6LAAC;gCAAI,WAAU;;;;;uCACb,IAAI,MAAM,KAAK,yBACjB,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;qDAElB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAEjB,IAAI,MAAM,KAAK,WAAW,SAAS;;;;;;;;;;;;;0BAIxC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;wBAAwB;wBAC1B,IAAI,KAAK,IAAI,UAAU,EAAE,kBAAkB;;;;;;;;;;;;YAIvD,0BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,CAAC;oBACR,EAAE,eAAe;oBACjB,YAAY;gBACd;;;;;;;;;;;;AAKV;GArJwB;KAAA", "debugId": null}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TFE/build-rot/build-rot-frontend/src/components/AddBotModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { X } from 'lucide-react';\n\ninterface Bot {\n  id: string;\n  name: string;\n  description: string;\n  discord_username: string;\n  status: 'active' | 'inactive';\n  created_at: string;\n}\n\ninterface AddBotModalProps {\n  onClose: () => void;\n  onBotAdded: (bot: Bot) => void;\n}\n\nexport default function AddBotModal({ onClose, onBotAdded }: AddBotModalProps) {\n  const [name, setName] = useState('');\n  const [description, setDescription] = useState('');\n  const [token, setToken] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const authToken = localStorage.getItem('token');\n      const response = await fetch('http://localhost:5001/api/bots', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${authToken}`\n        },\n        body: JSON.stringify({ name, description, token }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        onBotAdded(data.bot);\n      } else {\n        setError(data.error || 'Failed to add bot');\n      }\n    } catch (err) {\n      setError('Network error. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-md w-full mx-4\">\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">Add Discord Bot</h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"p-6 space-y-4\">\n          {error && (\n            <div className=\"p-3 bg-red-100 border border-red-400 text-red-700 rounded\">\n              {error}\n            </div>\n          )}\n\n          <div>\n            <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Bot Name *\n            </label>\n            <input\n              id=\"name\"\n              type=\"text\"\n              required\n              value={name}\n              onChange={(e) => setName(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"My Awesome Bot\"\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Description\n            </label>\n            <textarea\n              id=\"description\"\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"What does your bot do?\"\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"token\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Discord Bot Token *\n            </label>\n            <input\n              id=\"token\"\n              type=\"password\"\n              required\n              value={token}\n              onChange={(e) => setToken(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"Your bot token from Discord Developer Portal\"\n            />\n            <p className=\"mt-1 text-xs text-gray-500\">\n              Get your bot token from the{' '}\n              <a \n                href=\"https://discord.com/developers/applications\" \n                target=\"_blank\" \n                rel=\"noopener noreferrer\"\n                className=\"text-blue-600 hover:text-blue-800\"\n              >\n                Discord Developer Portal\n              </a>\n            </p>\n          </div>\n\n          <div className=\"flex justify-end space-x-3 pt-4\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? (\n                <div className=\"flex items-center\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Adding...\n                </div>\n              ) : (\n                'Add Bot'\n              )}\n            </button>\n          </div>\n        </form>\n\n        <div className=\"px-6 pb-6\">\n          <div className=\"bg-blue-50 border border-blue-200 rounded-md p-4\">\n            <h3 className=\"text-sm font-medium text-blue-900 mb-2\">How to get your bot token:</h3>\n            <ol className=\"text-xs text-blue-800 space-y-1\">\n              <li>1. Go to Discord Developer Portal</li>\n              <li>2. Create a new application or select existing one</li>\n              <li>3. Go to \"Bot\" section</li>\n              <li>4. Copy the bot token</li>\n              <li>5. Make sure your bot has the necessary permissions</li>\n            </ol>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAmBe,SAAS,YAAY,EAAE,OAAO,EAAE,UAAU,EAAoB;;IAC3E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,YAAY,aAAa,OAAO,CAAC;YACvC,MAAM,WAAW,MAAM,MAAM,kCAAkC;gBAC7D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,WAAW;gBACxC;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM;oBAAa;gBAAM;YAClD;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW,KAAK,GAAG;YACrB,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;wBACrC,uBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAO,WAAU;8CAA+C;;;;;;8CAG/E,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAQ;oCACR,OAAO;oCACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oCACvC,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAc,WAAU;8CAA+C;;;;;;8CAGtF,6LAAC;oCACC,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA+C;;;;;;8CAGhF,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAQ;oCACR,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,WAAU;oCACV,aAAY;;;;;;8CAEd,6LAAC;oCAAE,WAAU;;wCAA6B;wCACZ;sDAC5B,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAML,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,wBACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;4CAAuE;;;;;;+CAIxF;;;;;;;;;;;;;;;;;;8BAMR,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GAxJwB;KAAA", "debugId": null}}, {"offset": {"line": 925, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TFE/build-rot/build-rot-frontend/src/components/CodeEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Editor } from '@monaco-editor/react';\nimport { Play, Save, FileText, Zap } from 'lucide-react';\n\ninterface Bot {\n  id: string;\n  name: string;\n  description: string;\n  discord_username: string;\n  status: 'active' | 'inactive';\n  created_at: string;\n}\n\ninterface CodeEditorProps {\n  bot: Bot;\n  onBotUpdated: (bot: Bot) => void;\n}\n\nconst defaultCode = `// Welcome to Bot Rot! \n// This is a simple example to get you started.\n\n// Basic message response\nbot.onMessage((message) => {\n    if (message.content === '!hello') {\n        message.reply('Hello there! 👋');\n    }\n    \n    if (message.content === '!ping') {\n        message.reply(\\`Pong! My ping is \\${bot.client.ws.ping}ms\\`);\n    }\n});\n\n// Slash command example\nbot.command('userinfo', 'Get information about a user', [\n    { name: 'user', description: 'The user to get info about', type: 'user', required: false }\n])\n.run(async (interaction) => {\n    const user = interaction.options.getUser('user') || interaction.user;\n    const embed = bot.embed(\n        \\`User Info: \\${user.username}\\`,\n        \\`**ID:** \\${user.id}\\\\n**Created:** \\${user.createdAt.toDateString()}\\`,\n        '#00ff00'\n    );\n    await bot.reply(interaction, '', { embed });\n});\n\n// Welcome new members\nbot.onMemberJoin((member) => {\n    const channel = member.guild.channels.cache.find(ch => ch.name === 'welcome');\n    if (channel) {\n        const embed = bot.embed(\n            'New Member!',\n            \\`Welcome \\${member.user.username} to the server! 🎉\\`,\n            '#00ff00'\n        );\n        bot.sendMessage(channel, '', { embed });\n    }\n});\n\n// Register commands when bot is ready\nbot.onReady(() => {\n    console.log('Bot is ready!');\n    bot.registerCommands(); // Register slash commands globally\n});`;\n\nconst examples = [\n  {\n    name: 'Basic Commands',\n    description: 'Simple message responses and ping command',\n    code: `bot.onMessage((message) => {\n    if (message.content === '!hello') {\n        message.reply('Hello there! 👋');\n    }\n    \n    if (message.content === '!ping') {\n        message.reply(\\`Pong! My ping is \\${bot.client.ws.ping}ms\\`);\n    }\n});`\n  },\n  {\n    name: 'Slash Commands',\n    description: 'Modern Discord slash commands',\n    code: `bot.command('greet', 'Greet a user', [\n    { name: 'user', description: 'User to greet', type: 'user', required: true }\n])\n.run(async (interaction) => {\n    const user = interaction.options.getUser('user');\n    await bot.reply(interaction, \\`Hello \\${user.username}! 👋\\`);\n});\n\nbot.onReady(() => {\n    bot.registerCommands();\n});`\n  },\n  {\n    name: 'Embeds & Buttons',\n    description: 'Rich embeds with interactive buttons',\n    code: `bot.command('welcome', 'Send a welcome message with buttons')\n.run(async (interaction) => {\n    const embed = bot.embed(\n        'Welcome!',\n        'Click a button below to get started',\n        '#ff9900'\n    );\n    \n    const helpButton = bot.button('Get Help', 'help_button', 'Primary');\n    const communityButton = bot.button('Join Community', 'community_button', 'Success');\n    const row = bot.row(helpButton, communityButton);\n    \n    await bot.reply(interaction, '', { embed, components: [row] });\n});`\n  }\n];\n\nexport default function CodeEditor({ bot, onBotUpdated }: CodeEditorProps) {\n  const [code, setCode] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [deploying, setDeploying] = useState(false);\n  const [validating, setValidating] = useState(false);\n  const [validationResult, setValidationResult] = useState<{ valid: boolean; error?: string } | null>(null);\n\n  useEffect(() => {\n    fetchCode();\n  }, [bot.id]);\n\n  const fetchCode = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch(`http://localhost:5001/api/code/${bot.id}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setCode(data.code || defaultCode);\n      }\n    } catch (error) {\n      console.error('Failed to fetch code:', error);\n      setCode(defaultCode);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSave = async () => {\n    setSaving(true);\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch(`http://localhost:5001/api/code/${bot.id}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify({ code, language: 'javascript' }),\n      });\n\n      if (response.ok) {\n        console.log('Code saved successfully');\n      }\n    } catch (error) {\n      console.error('Failed to save code:', error);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleValidate = async () => {\n    setValidating(true);\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch(`http://localhost:5001/api/code/${bot.id}/validate`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify({ code }),\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        setValidationResult(result);\n      }\n    } catch (error) {\n      console.error('Failed to validate code:', error);\n    } finally {\n      setValidating(false);\n    }\n  };\n\n  const handleDeploy = async () => {\n    setDeploying(true);\n    try {\n      const token = localStorage.getItem('token');\n      \n      // Save code first\n      await fetch(`http://localhost:5001/api/code/${bot.id}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify({ code, language: 'javascript' }),\n      });\n\n      // Deploy bot\n      const response = await fetch(`http://localhost:5001/api/code/${bot.id}/deploy`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        onBotUpdated({ ...bot, status: 'active' });\n        console.log('Bot deployed successfully');\n      }\n    } catch (error) {\n      console.error('Failed to deploy bot:', error);\n    } finally {\n      setDeploying(false);\n    }\n  };\n\n  const insertExample = (example: typeof examples[0]) => {\n    setCode(example.code);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-96\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <button\n            onClick={handleSave}\n            disabled={saving}\n            className=\"flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\"\n          >\n            <Save className=\"h-4 w-4 mr-1\" />\n            {saving ? 'Saving...' : 'Save'}\n          </button>\n          \n          <button\n            onClick={handleValidate}\n            disabled={validating}\n            className=\"flex items-center px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50\"\n          >\n            <FileText className=\"h-4 w-4 mr-1\" />\n            {validating ? 'Validating...' : 'Validate'}\n          </button>\n          \n          <button\n            onClick={handleDeploy}\n            disabled={deploying}\n            className=\"flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50\"\n          >\n            <Zap className=\"h-4 w-4 mr-1\" />\n            {deploying ? 'Deploying...' : 'Deploy Bot'}\n          </button>\n        </div>\n      </div>\n\n      {validationResult && (\n        <div className={`p-3 rounded-md ${\n          validationResult.valid \n            ? 'bg-green-100 border border-green-400 text-green-700'\n            : 'bg-red-100 border border-red-400 text-red-700'\n        }`}>\n          {validationResult.valid ? '✅ Code syntax is valid' : `❌ ${validationResult.error}`}\n        </div>\n      )}\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-4\">\n        <div className=\"lg:col-span-3\">\n          <div className=\"border border-gray-300 rounded-md overflow-hidden\">\n            <Editor\n              height=\"500px\"\n              defaultLanguage=\"javascript\"\n              value={code}\n              onChange={(value) => setCode(value || '')}\n              theme=\"vs-dark\"\n              options={{\n                minimap: { enabled: false },\n                fontSize: 14,\n                lineNumbers: 'on',\n                roundedSelection: false,\n                scrollBeyondLastLine: false,\n                automaticLayout: true,\n              }}\n            />\n          </div>\n        </div>\n\n        <div className=\"space-y-4\">\n          <div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-3\">Examples</h3>\n            <div className=\"space-y-2\">\n              {examples.map((example, index) => (\n                <button\n                  key={index}\n                  onClick={() => insertExample(example)}\n                  className=\"w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-md border\"\n                >\n                  <div className=\"font-medium text-sm text-gray-900\">{example.name}</div>\n                  <div className=\"text-xs text-gray-600 mt-1\">{example.description}</div>\n                </button>\n              ))}\n            </div>\n          </div>\n\n          <div className=\"bg-blue-50 border border-blue-200 rounded-md p-4\">\n            <h4 className=\"text-sm font-medium text-blue-900 mb-2\">Bot Rot API</h4>\n            <div className=\"text-xs text-blue-800 space-y-1\">\n              <div><code>bot.onMessage(callback)</code></div>\n              <div><code>bot.onReady(callback)</code></div>\n              <div><code>bot.command(name, desc, options)</code></div>\n              <div><code>bot.embed(title, desc, color)</code></div>\n              <div><code>bot.button(label, id, style)</code></div>\n              <div><code>bot.reply(interaction, content)</code></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;;;AAJA;;;;AAoBA,MAAM,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6ClB,CAAC;AAEJ,MAAM,WAAW;IACf;QACE,MAAM;QACN,aAAa;QACb,MAAM,CAAC;;;;;;;;GAQR,CAAC;IACF;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,CAAC;;;;;;;;;;GAUR,CAAC;IACF;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,CAAC;;;;;;;;;;;;;GAaR,CAAC;IACF;CACD;AAEc,SAAS,WAAW,EAAE,GAAG,EAAE,YAAY,EAAmB;;IACvE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6C;IAEpG,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG;QAAC,IAAI,EAAE;KAAC;IAEX,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,IAAI,EAAE,EAAE,EAAE;gBACvE,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,QAAQ,KAAK,IAAI,IAAI;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,UAAU;QACV,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,IAAI,EAAE,EAAE,EAAE;gBACvE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM,UAAU;gBAAa;YACtD;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,iBAAiB;QACrB,cAAc;QACd,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,IAAI,EAAE,CAAC,SAAS,CAAC,EAAE;gBAChF,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAK;YAC9B;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,oBAAoB;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,eAAe;QACnB,aAAa;QACb,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,kBAAkB;YAClB,MAAM,MAAM,CAAC,+BAA+B,EAAE,IAAI,EAAE,EAAE,EAAE;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM,UAAU;gBAAa;YACtD;YAEA,aAAa;YACb,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,IAAI,EAAE,CAAC,OAAO,CAAC,EAAE;gBAC9E,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,aAAa;oBAAE,GAAG,GAAG;oBAAE,QAAQ;gBAAS;gBACxC,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,QAAQ,QAAQ,IAAI;IACtB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;;8CAEV,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCACf,SAAS,cAAc;;;;;;;sCAG1B,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;;8CAEV,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCACnB,aAAa,kBAAkB;;;;;;;sCAGlC,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;;8CAEV,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCACd,YAAY,iBAAiB;;;;;;;;;;;;;;;;;;YAKnC,kCACC,6LAAC;gBAAI,WAAW,CAAC,eAAe,EAC9B,iBAAiB,KAAK,GAClB,wDACA,iDACJ;0BACC,iBAAiB,KAAK,GAAG,2BAA2B,CAAC,EAAE,EAAE,iBAAiB,KAAK,EAAE;;;;;;0BAItF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gLAAA,CAAA,SAAM;gCACL,QAAO;gCACP,iBAAgB;gCAChB,OAAO;gCACP,UAAU,CAAC,QAAU,QAAQ,SAAS;gCACtC,OAAM;gCACN,SAAS;oCACP,SAAS;wCAAE,SAAS;oCAAM;oCAC1B,UAAU;oCACV,aAAa;oCACb,kBAAkB;oCAClB,sBAAsB;oCACtB,iBAAiB;gCACnB;;;;;;;;;;;;;;;;kCAKN,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;gDAEC,SAAS,IAAM,cAAc;gDAC7B,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;kEAAqC,QAAQ,IAAI;;;;;;kEAChE,6LAAC;wDAAI,WAAU;kEAA8B,QAAQ,WAAW;;;;;;;+CAL3D;;;;;;;;;;;;;;;;0CAWb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAI,cAAA,6LAAC;8DAAK;;;;;;;;;;;0DACX,6LAAC;0DAAI,cAAA,6LAAC;8DAAK;;;;;;;;;;;0DACX,6LAAC;0DAAI,cAAA,6LAAC;8DAAK;;;;;;;;;;;0DACX,6LAAC;0DAAI,cAAA,6LAAC;8DAAK;;;;;;;;;;;0DACX,6LAAC;0DAAI,cAAA,6LAAC;8DAAK;;;;;;;;;;;0DACX,6LAAC;0DAAI,cAAA,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzB;GA9NwB;KAAA", "debugId": null}}, {"offset": {"line": 1482, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TFE/build-rot/build-rot-frontend/src/components/Dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Plus, Bot, Play, Square, Settings, Code, LogOut, Blocks } from 'lucide-react';\nimport BotCard from './BotCard';\nimport AddBotModal from './AddBotModal';\nimport CodeEditor from './CodeEditor';\nimport VisualBuilder from './VisualBuilder';\n\ninterface User {\n  id: string;\n  email: string;\n}\n\ninterface Bot {\n  id: string;\n  name: string;\n  description: string;\n  discord_username: string;\n  status: 'active' | 'inactive';\n  created_at: string;\n}\n\ninterface DashboardProps {\n  user: User;\n  onLogout: () => void;\n}\n\nexport default function Dashboard({ user, onLogout }: DashboardProps) {\n  const [bots, setBots] = useState<Bot[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [showAddBot, setShowAddBot] = useState(false);\n  const [selectedBot, setSelectedBot] = useState<Bot | null>(null);\n  const [activeTab, setActiveTab] = useState<'overview' | 'code' | 'visual'>('overview');\n\n  useEffect(() => {\n    fetchBots();\n  }, []);\n\n  const fetchBots = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch('http://localhost:5001/api/bots', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setBots(data.bots);\n      }\n    } catch (error) {\n      console.error('Failed to fetch bots:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem('token');\n    onLogout();\n  };\n\n  const handleBotAdded = (newBot: Bot) => {\n    setBots([...bots, newBot]);\n    setShowAddBot(false);\n  };\n\n  const handleBotUpdated = (updatedBot: Bot) => {\n    setBots(bots.map(bot => bot.id === updatedBot.id ? updatedBot : bot));\n    if (selectedBot?.id === updatedBot.id) {\n      setSelectedBot(updatedBot);\n    }\n  };\n\n  const handleBotDeleted = (botId: string) => {\n    setBots(bots.filter(bot => bot.id !== botId));\n    if (selectedBot?.id === botId) {\n      setSelectedBot(null);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Bot className=\"h-8 w-8 text-blue-600 mr-3\" />\n              <h1 className=\"text-xl font-semibold text-gray-900\">Build Rot</h1>\n            </div>\n            \n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-600\">Welcome, {user.email}</span>\n              <button\n                onClick={handleLogout}\n                className=\"flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900\"\n              >\n                <LogOut className=\"h-4 w-4 mr-1\" />\n                Logout\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {selectedBot ? (\n          <div className=\"space-y-6\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => setSelectedBot(null)}\n                  className=\"text-blue-600 hover:text-blue-800\"\n                >\n                  ← Back to Dashboard\n                </button>\n                <h2 className=\"text-2xl font-bold text-gray-900\">{selectedBot.name}</h2>\n                <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                  selectedBot.status === 'active' \n                    ? 'bg-green-100 text-green-800' \n                    : 'bg-gray-100 text-gray-800'\n                }`}>\n                  {selectedBot.status}\n                </span>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow\">\n              <div className=\"border-b border-gray-200\">\n                <nav className=\"-mb-px flex\">\n                  <button\n                    onClick={() => setActiveTab('overview')}\n                    className={`py-2 px-4 border-b-2 font-medium text-sm ${\n                      activeTab === 'overview'\n                        ? 'border-blue-500 text-blue-600'\n                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                    }`}\n                  >\n                    Overview\n                  </button>\n                  <button\n                    onClick={() => setActiveTab('visual')}\n                    className={`py-2 px-4 border-b-2 font-medium text-sm ${\n                      activeTab === 'visual'\n                        ? 'border-blue-500 text-blue-600'\n                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                    }`}\n                  >\n                    <Blocks className=\"h-4 w-4 inline mr-1\" />\n                    Visual Builder\n                  </button>\n                  <button\n                    onClick={() => setActiveTab('code')}\n                    className={`py-2 px-4 border-b-2 font-medium text-sm ${\n                      activeTab === 'code'\n                        ? 'border-blue-500 text-blue-600'\n                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                    }`}\n                  >\n                    <Code className=\"h-4 w-4 inline mr-1\" />\n                    Code Editor\n                  </button>\n                </nav>\n              </div>\n\n              <div className=\"p-6\">\n                {activeTab === 'overview' ? (\n                  <div className=\"space-y-6\">\n                    <div>\n                      <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Bot Information</h3>\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700\">Name</label>\n                          <p className=\"mt-1 text-sm text-gray-900\">{selectedBot.name}</p>\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700\">Discord Username</label>\n                          <p className=\"mt-1 text-sm text-gray-900\">{selectedBot.discord_username}</p>\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700\">Status</label>\n                          <p className=\"mt-1 text-sm text-gray-900 capitalize\">{selectedBot.status}</p>\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700\">Created</label>\n                          <p className=\"mt-1 text-sm text-gray-900\">\n                            {new Date(selectedBot.created_at).toLocaleDateString()}\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n\n                    {selectedBot.description && (\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700\">Description</label>\n                        <p className=\"mt-1 text-sm text-gray-900\">{selectedBot.description}</p>\n                      </div>\n                    )}\n                  </div>\n                ) : activeTab === 'visual' ? (\n                  <VisualBuilder\n                    bot={selectedBot}\n                    onBotUpdated={handleBotUpdated}\n                  />\n                ) : (\n                  <CodeEditor\n                    bot={selectedBot}\n                    onBotUpdated={handleBotUpdated}\n                  />\n                )}\n              </div>\n            </div>\n          </div>\n        ) : (\n          <div className=\"space-y-6\">\n            <div className=\"flex justify-between items-center\">\n              <h2 className=\"text-2xl font-bold text-gray-900\">Your Discord Bots</h2>\n              <button\n                onClick={() => setShowAddBot(true)}\n                className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n              >\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Add Bot\n              </button>\n            </div>\n\n            {bots.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <Bot className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No bots yet</h3>\n                <p className=\"text-gray-600 mb-4\">Get started by adding your first Discord bot</p>\n                <button\n                  onClick={() => setShowAddBot(true)}\n                  className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n                >\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Add Your First Bot\n                </button>\n              </div>\n            ) : (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {bots.map((bot) => (\n                  <BotCard\n                    key={bot.id}\n                    bot={bot}\n                    onSelect={() => setSelectedBot(bot)}\n                    onUpdate={handleBotUpdated}\n                    onDelete={handleBotDeleted}\n                  />\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n      </main>\n\n      {showAddBot && (\n        <AddBotModal\n          onClose={() => setShowAddBot(false)}\n          onBotAdded={handleBotAdded}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;AANA;;;;;;;AA4Be,SAAS,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAkB;;IAClE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC;IAE3E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,kCAAkC;gBAC7D,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,QAAQ,KAAK,IAAI;YACnB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,QAAQ;eAAI;YAAM;SAAO;QACzB,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,WAAW,EAAE,GAAG,aAAa;QAChE,IAAI,aAAa,OAAO,WAAW,EAAE,EAAE;YACrC,eAAe;QACjB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QACtC,IAAI,aAAa,OAAO,OAAO;YAC7B,eAAe;QACjB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAGtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CAAwB;4CAAU,KAAK,KAAK;;;;;;;kDAC5D,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7C,6LAAC;gBAAK,WAAU;0BACb,4BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCAAG,WAAU;kDAAoC,YAAY,IAAI;;;;;;kDAClE,6LAAC;wCAAK,WAAW,CAAC,2CAA2C,EAC3D,YAAY,MAAM,KAAK,WACnB,gCACA,6BACJ;kDACC,YAAY,MAAM;;;;;;;;;;;;;;;;;sCAKzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,aACV,kCACA,8EACJ;0DACH;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,WACV,kCACA,8EACJ;;kEAEF,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAG5C,6LAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,SACV,kCACA,8EACJ;;kEAEF,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;;;;;;;;;;;;8CAM9C,6LAAC;oCAAI,WAAU;8CACZ,cAAc,2BACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAA0C;;;;;;kFAC3D,6LAAC;wEAAE,WAAU;kFAA8B,YAAY,IAAI;;;;;;;;;;;;0EAE7D,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAA0C;;;;;;kFAC3D,6LAAC;wEAAE,WAAU;kFAA8B,YAAY,gBAAgB;;;;;;;;;;;;0EAEzE,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAA0C;;;;;;kFAC3D,6LAAC;wEAAE,WAAU;kFAAyC,YAAY,MAAM;;;;;;;;;;;;0EAE1E,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAA0C;;;;;;kFAC3D,6LAAC;wEAAE,WAAU;kFACV,IAAI,KAAK,YAAY,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;4CAM3D,YAAY,WAAW,kBACtB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA0C;;;;;;kEAC3D,6LAAC;wDAAE,WAAU;kEAA8B,YAAY,WAAW;;;;;;;;;;;;;;;;;+CAItE,cAAc,yBAChB,6LAAC;wCACC,KAAK;wCACL,cAAc;;;;;6DAGhB,6LAAC,mIAAA,CAAA,UAAU;wCACT,KAAK;wCACL,cAAc;;;;;;;;;;;;;;;;;;;;;;yCAOxB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCACC,SAAS,IAAM,cAAc;oCAC7B,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;wBAKpC,KAAK,MAAM,KAAK,kBACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC;oCACC,SAAS,IAAM,cAAc;oCAC7B,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;iDAKrC,6LAAC;4BAAI,WAAU;sCACZ,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC,gIAAA,CAAA,UAAO;oCAEN,KAAK;oCACL,UAAU,IAAM,eAAe;oCAC/B,UAAU;oCACV,UAAU;mCAJL,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;YAaxB,4BACC,6LAAC,oIAAA,CAAA,UAAW;gBACV,SAAS,IAAM,cAAc;gBAC7B,YAAY;;;;;;;;;;;;AAKtB;GAtPwB;KAAA", "debugId": null}}, {"offset": {"line": 2105, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/TFE/build-rot/build-rot-frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport AuthForm from '@/components/AuthForm';\nimport Dashboard from '@/components/Dashboard';\n\nexport default function Home() {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      fetch('http://localhost:5001/api/auth/me', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      })\n      .then(res => res.json())\n      .then(data => {\n        if (data.user) {\n          setUser(data.user);\n        } else {\n          localStorage.removeItem('token');\n        }\n      })\n      .catch(() => {\n        localStorage.removeItem('token');\n      })\n      .finally(() => {\n        setLoading(false);\n      });\n    } else {\n      setLoading(false);\n    }\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {user ? (\n        <Dashboard user={user} onLogout={() => setUser(null)} />\n      ) : (\n        <AuthForm onLogin={setUser} />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,OAAO;gBACT,MAAM,qCAAqC;oBACzC,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBACpC;gBACF,GACC,IAAI;sCAAC,CAAA,MAAO,IAAI,IAAI;qCACpB,IAAI;sCAAC,CAAA;wBACJ,IAAI,KAAK,IAAI,EAAE;4BACb,QAAQ,KAAK,IAAI;wBACnB,OAAO;4BACL,aAAa,UAAU,CAAC;wBAC1B;oBACF;qCACC,KAAK;sCAAC;wBACL,aAAa,UAAU,CAAC;oBAC1B;qCACC,OAAO;sCAAC;wBACP,WAAW;oBACb;;YACF,OAAO;gBACL,WAAW;YACb;QACF;yBAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,qBACC,6LAAC,kIAAA,CAAA,UAAS;YAAC,MAAM;YAAM,UAAU,IAAM,QAAQ;;;;;iCAE/C,6LAAC,iIAAA,CAAA,UAAQ;YAAC,SAAS;;;;;;;;;;;AAI3B;GAhDwB;KAAA", "debugId": null}}]}