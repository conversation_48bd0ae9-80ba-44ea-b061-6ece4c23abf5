#!/bin/bash

echo "🤖 Build Rot Setup Script"
echo "========================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
cd build-rot-frontend
if npm install; then
    echo "✅ Frontend dependencies installed"
else
    echo "❌ Failed to install frontend dependencies"
    exit 1
fi

# Install backend dependencies
echo "📦 Installing backend dependencies..."
cd ../backend
if npm install; then
    echo "✅ Backend dependencies installed"
else
    echo "❌ Failed to install backend dependencies"
    exit 1
fi

# Install bot library dependencies
echo "📦 Installing bot library dependencies..."
cd ../shared/bot-rot-lib
if npm install; then
    echo "✅ Bot library dependencies installed"
else
    echo "❌ Failed to install bot library dependencies"
    exit 1
fi

# Create environment file
cd ../../backend
if [ ! -f .env ]; then
    cp .env.example .env
    echo "📝 Created .env file from template"
    echo "⚠️  Please edit backend/.env with your Supabase credentials"
else
    echo "📝 .env file already exists"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Set up a Supabase project and run the database schema"
echo "2. Edit backend/.env with your Supabase credentials"
echo "3. Start the development servers:"
echo "   - Backend: cd backend && npm run dev"
echo "   - Frontend: cd build-rot-frontend && npm run dev"
echo ""
echo "Visit http://localhost:3000 to access Build Rot!"
