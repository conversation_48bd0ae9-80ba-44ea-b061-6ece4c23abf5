
const { createBot } = require('../../shared/bot-rot-lib');

const bot = createBot('MTM4MTcyMDI5MDk0OTU5NTI1Nw.G4Aqtu.k-HHFLrQxFTEeT5d6o4NgRlfRrjnkfQVZNZuGE');

// Generated code from Visual Builder

const BOT_ID = 'ef53363b-2315-4cb3-a4ff-96acf8ecc31c';
const BOT_NAME = 'boty';
const SERVER_URL = 'http://localhost:5001';

bot.onMessage((message) => {
    // Extract variables from message event
    const author_name = message.author.username;
    const author_id = message.author.id;
    const content = message.content;
    const channel_name = message.channel.name;
    const guild_name = message.guild ? message.guild.name : "DM";

    // Send message
    bot.sendMessage(message.channel, 'Hello');

});



bot.start().catch(console.error);
        