{"name": "build-rot-backend", "version": "1.0.0", "description": "Backend for Build Rot Discord bot builder platform", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["discord", "bot", "builder", "platform"], "author": "Build Rot", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.50.3", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "discord.js": "^14.21.0", "dotenv": "^17.0.1", "express": "^4.21.2", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.10", "@types/uuid": "^10.0.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}