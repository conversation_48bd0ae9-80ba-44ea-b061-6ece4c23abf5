const express = require('express');
const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const { run, get, all } = require('../database');

const router = express.Router();

const authenticateUser = async (req, res, next) => {
    try {
        const token = req.headers.authorization?.replace('Bearer ', '');

        if (!token) {
            return res.status(401).json({ error: 'No token provided' });
        }

        // Verify JWT token
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key');

        // Get user from database
        const user = await get('SELECT id, email FROM users WHERE id = ?', [decoded.userId]);
        if (!user) {
            return res.status(401).json({ error: 'User not found' });
        }

        req.user = user;
        next();
    } catch (error) {
        if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
            return res.status(401).json({ error: 'Invalid token' });
        }
        res.status(401).json({ error: 'Authentication failed' });
    }
};

router.use(authenticateUser);

router.get('/:botId', async (req, res) => {
    try {
        const { botId } = req.params;

        const codeData = await get(
            'SELECT code, language, flow_data FROM bot_code WHERE bot_id = ? AND user_id = ?',
            [botId, req.user.id]
        );

        res.json({
            code: codeData?.code || '',
            language: codeData?.language || 'javascript',
            flowData: codeData?.flow_data || null
        });
    } catch (error) {
        console.error('Get code error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

router.post('/:botId', async (req, res) => {
    try {
        const { botId } = req.params;
        const { code, language = 'javascript', flowData } = req.body;

        if (!code) {
            return res.status(400).json({ error: 'Code is required' });
        }

        // Check if bot exists and belongs to user
        const bot = await get(
            'SELECT id FROM bots WHERE id = ? AND user_id = ?',
            [botId, req.user.id]
        );

        if (!bot) {
            return res.status(404).json({ error: 'Bot not found' });
        }

        // Check if code already exists
        const existingCode = await get(
            'SELECT id FROM bot_code WHERE bot_id = ?',
            [botId]
        );

        if (existingCode) {
            // Update existing code
            await run(
                'UPDATE bot_code SET code = ?, language = ?, flow_data = ?, updated_at = CURRENT_TIMESTAMP WHERE bot_id = ?',
                [code, language, flowData || null, botId]
            );
        } else {
            // Insert new code
            await run(
                'INSERT INTO bot_code (id, bot_id, user_id, code, language, flow_data) VALUES (?, ?, ?, ?, ?, ?)',
                [uuidv4(), botId, req.user.id, code, language, flowData || null]
            );
        }

        res.json({
            message: 'Code saved successfully'
        });
    } catch (error) {
        console.error('Save code error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

router.post('/:botId/deploy', async (req, res) => {
    try {
        const { botId } = req.params;

        // Get bot with token
        const bot = await get(
            'SELECT * FROM bots WHERE id = ? AND user_id = ?',
            [botId, req.user.id]
        );

        if (!bot) {
            return res.status(404).json({ error: 'Bot not found' });
        }

        // Get bot code
        const codeData = await get(
            'SELECT code FROM bot_code WHERE bot_id = ? AND user_id = ?',
            [botId, req.user.id]
        );

        if (!codeData) {
            return res.status(404).json({ error: 'No code found for this bot' });
        }

        const botDir = path.join(__dirname, '../../deployed-bots', botId);
        await fs.mkdir(botDir, { recursive: true });

        const botCode = `
const { createBot } = require('../../shared/bot-rot-lib');

const bot = createBot('${bot.token}');

${codeData.code}

bot.start().catch(console.error);
        `;

        await fs.writeFile(path.join(botDir, 'bot.js'), botCode);

        // Update bot status
        await run(
            'UPDATE bots SET status = ?, last_deployed = CURRENT_TIMESTAMP WHERE id = ?',
            ['active', botId]
        );

        res.json({
            message: 'Bot deployed successfully',
            status: 'active'
        });
    } catch (error) {
        console.error('Deploy bot error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

router.post('/:botId/validate', async (req, res) => {
    try {
        const { code } = req.body;

        if (!code) {
            return res.status(400).json({ error: 'Code is required' });
        }

        try {
            new Function(code);
            res.json({ 
                valid: true,
                message: 'Code syntax is valid'
            });
        } catch (syntaxError) {
            res.json({ 
                valid: false,
                error: syntaxError.message
            });
        }
    } catch (error) {
        console.error('Validate code error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

module.exports = router;
