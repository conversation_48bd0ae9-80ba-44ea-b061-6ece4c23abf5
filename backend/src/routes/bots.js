const express = require('express');
const { createClient } = require('@supabase/supabase-js');
const { Client, GatewayIntentBits } = require('discord.js');
const { v4: uuidv4 } = require('uuid');

const router = express.Router();

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_ANON_KEY
);

const authenticateUser = async (req, res, next) => {
    try {
        const token = req.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
            return res.status(401).json({ error: 'No token provided' });
        }

        const { data, error } = await supabase.auth.getUser(token);

        if (error) {
            return res.status(401).json({ error: 'Invalid token' });
        }

        req.user = data.user;
        next();
    } catch (error) {
        res.status(401).json({ error: 'Authentication failed' });
    }
};

router.use(authenticateUser);

router.get('/', async (req, res) => {
    try {
        const { data, error } = await supabase
            .from('bots')
            .select('*')
            .eq('user_id', req.user.id);

        if (error) {
            return res.status(500).json({ error: 'Failed to fetch bots' });
        }

        res.json({ bots: data || [] });
    } catch (error) {
        console.error('Get bots error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

router.post('/', async (req, res) => {
    try {
        const { name, token, description } = req.body;

        if (!name || !token) {
            return res.status(400).json({ error: 'Name and token are required' });
        }

        const testClient = new Client({
            intents: [GatewayIntentBits.Guilds]
        });

        try {
            await testClient.login(token);
            const botUser = testClient.user;
            await testClient.destroy();

            const botData = {
                id: uuidv4(),
                user_id: req.user.id,
                name,
                description: description || '',
                token,
                discord_id: botUser.id,
                discord_username: botUser.username,
                status: 'inactive',
                created_at: new Date().toISOString()
            };

            const { data, error } = await supabase
                .from('bots')
                .insert([botData])
                .select()
                .single();

            if (error) {
                return res.status(500).json({ error: 'Failed to save bot' });
            }

            const responseBot = { ...data };
            delete responseBot.token;

            res.status(201).json({ 
                message: 'Bot added successfully',
                bot: responseBot
            });
        } catch (discordError) {
            res.status(400).json({ error: 'Invalid Discord bot token' });
        }
    } catch (error) {
        console.error('Add bot error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

router.put('/:botId', async (req, res) => {
    try {
        const { botId } = req.params;
        const { name, description } = req.body;

        const { data, error } = await supabase
            .from('bots')
            .update({ name, description })
            .eq('id', botId)
            .eq('user_id', req.user.id)
            .select()
            .single();

        if (error) {
            return res.status(500).json({ error: 'Failed to update bot' });
        }

        if (!data) {
            return res.status(404).json({ error: 'Bot not found' });
        }

        const responseBot = { ...data };
        delete responseBot.token;

        res.json({ 
            message: 'Bot updated successfully',
            bot: responseBot
        });
    } catch (error) {
        console.error('Update bot error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

router.delete('/:botId', async (req, res) => {
    try {
        const { botId } = req.params;

        const { error } = await supabase
            .from('bots')
            .delete()
            .eq('id', botId)
            .eq('user_id', req.user.id);

        if (error) {
            return res.status(500).json({ error: 'Failed to delete bot' });
        }

        res.json({ message: 'Bot deleted successfully' });
    } catch (error) {
        console.error('Delete bot error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

router.post('/:botId/start', async (req, res) => {
    try {
        const { botId } = req.params;

        const { data: bot, error } = await supabase
            .from('bots')
            .select('*')
            .eq('id', botId)
            .eq('user_id', req.user.id)
            .single();

        if (error || !bot) {
            return res.status(404).json({ error: 'Bot not found' });
        }

        await supabase
            .from('bots')
            .update({ status: 'active' })
            .eq('id', botId);

        res.json({ message: 'Bot started successfully' });
    } catch (error) {
        console.error('Start bot error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

router.post('/:botId/stop', async (req, res) => {
    try {
        const { botId } = req.params;

        await supabase
            .from('bots')
            .update({ status: 'inactive' })
            .eq('id', botId)
            .eq('user_id', req.user.id);

        res.json({ message: 'Bot stopped successfully' });
    } catch (error) {
        console.error('Stop bot error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

module.exports = router;
