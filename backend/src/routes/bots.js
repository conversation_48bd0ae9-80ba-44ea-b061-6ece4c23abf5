const express = require('express');
const { Client, GatewayIntentBits } = require('discord.js');
const { v4: uuidv4 } = require('uuid');
const jwt = require('jsonwebtoken');
const { run, get, all } = require('../database');

const router = express.Router();

const authenticateUser = async (req, res, next) => {
    try {
        const token = req.headers.authorization?.replace('Bearer ', '');

        if (!token) {
            return res.status(401).json({ error: 'No token provided' });
        }

        // Verify JWT token
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key');

        // Get user from database
        const user = await get('SELECT id, email FROM users WHERE id = ?', [decoded.userId]);
        if (!user) {
            return res.status(401).json({ error: 'User not found' });
        }

        req.user = user;
        next();
    } catch (error) {
        if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
            return res.status(401).json({ error: 'Invalid token' });
        }
        res.status(401).json({ error: 'Authentication failed' });
    }
};

router.use(authenticateUser);

router.get('/', async (req, res) => {
    try {
        const bots = await all(
            'SELECT id, name, description, discord_id, discord_username, status, created_at, updated_at, last_deployed FROM bots WHERE user_id = ?',
            [req.user.id]
        );

        res.json({ bots: bots || [] });
    } catch (error) {
        console.error('Get bots error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

router.post('/', async (req, res) => {
    try {
        const { name, token, description } = req.body;

        if (!name || !token) {
            return res.status(400).json({ error: 'Name and token are required' });
        }

        const testClient = new Client({
            intents: [GatewayIntentBits.Guilds]
        });

        try {
            await testClient.login(token);
            const botUser = testClient.user;
            await testClient.destroy();

            const botId = uuidv4();
            await run(
                'INSERT INTO bots (id, user_id, name, description, token, discord_id, discord_username, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
                [botId, req.user.id, name, description || '', token, botUser.id, botUser.username, 'inactive']
            );

            // Get the created bot (without token)
            const bot = await get(
                'SELECT id, name, description, discord_id, discord_username, status, created_at, updated_at FROM bots WHERE id = ?',
                [botId]
            );

            res.status(201).json({
                message: 'Bot added successfully',
                bot: bot
            });
        } catch (discordError) {
            res.status(400).json({ error: 'Invalid Discord bot token' });
        }
    } catch (error) {
        console.error('Add bot error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

router.put('/:botId', async (req, res) => {
    try {
        const { botId } = req.params;
        const { name, description } = req.body;

        const result = await run(
            'UPDATE bots SET name = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?',
            [name, description, botId, req.user.id]
        );

        if (result.changes === 0) {
            return res.status(404).json({ error: 'Bot not found' });
        }

        // Get updated bot
        const bot = await get(
            'SELECT id, name, description, discord_id, discord_username, status, created_at, updated_at FROM bots WHERE id = ?',
            [botId]
        );

        res.json({
            message: 'Bot updated successfully',
            bot: bot
        });
    } catch (error) {
        console.error('Update bot error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

router.delete('/:botId', async (req, res) => {
    try {
        const { botId } = req.params;

        const result = await run(
            'DELETE FROM bots WHERE id = ? AND user_id = ?',
            [botId, req.user.id]
        );

        if (result.changes === 0) {
            return res.status(404).json({ error: 'Bot not found' });
        }

        res.json({ message: 'Bot deleted successfully' });
    } catch (error) {
        console.error('Delete bot error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

router.post('/:botId/start', async (req, res) => {
    try {
        const { botId } = req.params;

        const bot = await get(
            'SELECT id FROM bots WHERE id = ? AND user_id = ?',
            [botId, req.user.id]
        );

        if (!bot) {
            return res.status(404).json({ error: 'Bot not found' });
        }

        await run(
            'UPDATE bots SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            ['active', botId]
        );

        res.json({ message: 'Bot started successfully' });
    } catch (error) {
        console.error('Start bot error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

router.post('/:botId/stop', async (req, res) => {
    try {
        const { botId } = req.params;

        const result = await run(
            'UPDATE bots SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?',
            ['inactive', botId, req.user.id]
        );

        if (result.changes === 0) {
            return res.status(404).json({ error: 'Bot not found' });
        }

        res.json({ message: 'Bot stopped successfully' });
    } catch (error) {
        console.error('Stop bot error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

module.exports = router;
